import { Component, Input } from '@angular/core';
import { ControlContainer, NgModelGroup } from '@angular/forms';
import { ElectrolyteMetrics } from '@com/services/cell-design-metrics.service';
import { CellDesign } from '@com/services/cell-design.service';
import { SummaryTableRowCell } from '@com/app/cell-design-page/summary-table/summary-table.component';
import { NotifierService } from '@com/services/notifier.service';

const CONTROL_NAMES = {
    electrolytAmount: 'electrolyt_amount',
} as const;

@Component({
    selector: 'com-electrolyte-editor[design][metrics][loading]',
    templateUrl: './electrolyte-editor.component.html',
    styleUrls: ['electrolyte-editor.component.scss'],

    // this is important for the change detection to work across components
    // makes this component use the same NgForm as the parent component
    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],
})
export class ElectrolyteEditorComponent {
    @Input()
    public loading: boolean;

    @Input()
    public design: CellDesign | undefined;

    @Input()
    public metrics: ElectrolyteMetrics | undefined;

    public readonly controlNames = CONTROL_NAMES;

    public constructor(private readonly _notifier: NotifierService) {}

    public onCellValueChange(cell: SummaryTableRowCell): void {
        if (cell.editable && this.design) {
            this.design.agingTableEditValues[cell.editable.id] = +cell.editable.value;
            this._notifier.refreshMetrics();
        }
    }

    public onCellValueReset(cell: SummaryTableRowCell): void {
        if (cell.editable && this.design) {
            delete this.design.agingTableEditValues[cell.editable.id];
            this._notifier.refreshMetrics();
        }
    }

    public hasCellBeenEdited = (cellId: string): boolean => {
        return this.design != null && this.design.agingTableEditValues[cellId] != null;
    };
}
