import { Injectable } from '@angular/core';
import { MsalService } from '@azure/msal-angular';
import { AccountInfo } from '@azure/msal-browser';

export const enum UserRolesEnum {
    user = 'user',
    release = 'release',
    admin = 'admin',
}

export const ROLES_DATA_NAME = 'requiredRoles';

@Injectable({ providedIn: 'root' })
export class AuthService {
    public constructor(private readonly _msalService: MsalService) {}

    public isLoggedIn(): boolean {
        const account = this.getAccount();

        return account != null;
    }

    public hasRole(role: UserRolesEnum): boolean {
        const account = this.getAccount();
        const roles = account?.idTokenClaims?.roles;

        if (roles && roles.indexOf(role) != null) {
            return true;
        }

        return false;
    }

    public hasAnyRole(...roles: UserRolesEnum[]): boolean {
        return roles.find((role) => this.hasRole(role)) != null;
    }

    private getAccount(): AccountInfo | null {
        let account = this._msalService.instance.getActiveAccount();
        account = account ?? this._msalService.instance.getAllAccounts()[0];

        return account;
    }
}
