import { concatMap, Observable, OperatorFunction } from 'rxjs';

export function blobToDataUrl(): OperatorFunction<Blob, string> {
    return concatMap((value) => {
        return new Observable<string>((subscriber) => {
            const reader = new FileReader();
            reader.onload = (): void => {
                subscriber.next(reader.result as string);
                subscriber.complete();
            };

            reader.readAsDataURL(value);
        });
    });
}
