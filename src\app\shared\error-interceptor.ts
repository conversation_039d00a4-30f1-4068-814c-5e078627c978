import { Injectable } from '@angular/core';
import {
    HttpE<PERSON>,
    HttpInterceptor,
    HttpHandler,
    HttpRequest,
    HttpContextToken,
    HttpStatusCode,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { MessageService } from 'primeng/api';
import { ToastPositionEnum, ToastSeverityEnum } from '@com/app/prime-ng';

export const UNHANDLED_ERROR_CODES = new HttpContextToken<HttpStatusCode[]>(() => [] as HttpStatusCode[]);

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
    public constructor(private _toasts: MessageService) {}

    public intercept<TRequest, TResponse>(
        request: HttpRequest<TRequest>,
        next: HttpHandler
    ): Observable<HttpEvent<TResponse>> {
        return next.handle(request).pipe(
            catchError((error, _caught) => {
                let errorMsg = '';
                if (error.error instanceof ErrorEvent) {
                    // Client side error
                    errorMsg = $localize`:@@common.errorWithMessage:Error: ${error.error.message}:message:`;
                } else {
                    // Server side error
                    errorMsg = $localize`:@@common.errorWithCode:Error Code: ${error.status}:statusCode:, Message: ${error.message}:message:`;
                }

                if (request.context.get(UNHANDLED_ERROR_CODES).indexOf(error.status) === -1) {
                    this._toasts.add({
                        key: ToastPositionEnum.topCenter,
                        severity: ToastSeverityEnum.error,
                        summary: $localize`:@@common.unexpectedError:Unexpected Error`,
                        detail: errorMsg,
                    });
                }

                return throwError(() => error);
            })
        );
    }
}
