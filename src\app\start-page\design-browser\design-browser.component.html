<p-table
    #dt
    [value]="designs"
    [paginator]="true"
    [rows]="10"
    [rowsPerPageOptions]="[10, 25, 50]"
    [loading]="loading"
    [filterDelay]="300"
    [resizableColumns]="true"
    [(selection)]="selectedDesigns"
    [sortField]="fields.designId"
    [sortOrder]="defaultOrder"
>
    >
    <ng-template pTemplate="caption">
        <div class="table-header flex justify-content-between">
            <a
                pButton
                type="button"
                i18n-label="@@common.startNewDesign"
                label="Start new Design"
                routerLink="/cell-design"
            ></a>
            <p-button
                i18n-label="@@common.export"
                label="Export"
                icon="pi pi-file-export"
                [disabled]="!selectedDesigns || !selectedDesigns.length"
                (click)="exportSelectedDesigns()"
            ></p-button>
        </div>
    </ng-template>
    <ng-template pTemplate="header">
        <tr>
            <th></th>
            <th [pSortableColumn]="fields.designId">
                <span i18n="@@common.design">Design</span>
                <p-sortIcon [field]="fields.designId"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.name">
                <span i18n="@@common.name">Name</span>
                <p-sortIcon [field]="fields.name"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.createdAt">
                <span i18n="@@common.createdAt">Erstelldatum</span>
                <p-sortIcon [field]="fields.createdAt"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.createdBy">
                <span i18n="@@common.createdBy">Erstellt von</span>
                <p-sortIcon [field]="fields.createdBy"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.projectName">
                <span i18n="@@common.projectName">Projektname</span>
                <p-sortIcon [field]="fields.projectName"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.projectState">
                <span i18n="@@common.projectState">Musterstand</span>
                <p-sortIcon [field]="fields.projectState"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.releasedAt">
                <span i18n="@@common.releasedDate">Freigabendatum</span>
                <p-sortIcon [field]="fields.releasedAt"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.releasedBy">
                <span i18n="@@common.releasedBy">Freigeben von</span>
                <p-sortIcon [field]="fields.releasedBy"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.released">
                <span i18n="@@common.releaseStatus">Freigabenstatus</span>
                <p-sortIcon [field]="fields.released"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.partNumber">
                <span i18n="@@common.partNumber">Part Nummer</span>
                <p-sortIcon [field]="fields.partNumber"></p-sortIcon>
            </th>
            <th [pSortableColumn]="fields.description">
                <span i18n="@@common.description">Beschreibung</span>
                <p-sortIcon [field]="fields.description"></p-sortIcon>
            </th>
            <th style="width: 4rem">
                <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
            </th>
        </tr>
        <tr>
            <ng-template #containsFilter let-field>
                <div class="flex justify-content-between">
                    <input
                        pInputText
                        #input
                        type="text"
                        style="width: 85%"
                        (input)="dt.filter($any($event).target.value, field, matchModes.CONTAINS)"
                    />
                    <button
                        *ngIf="input.value != null && input.value !== ''"
                        type="button"
                        class="p-column-filter-clear-button p-link p-button-sm"
                        (click)="dt.filter('', field, matchModes.CONTAINS); input.value = ''"
                    >
                        <span class="pi pi-filter-slash"></span>
                    </button>
                </div>
            </ng-template>

            <th></th>
            <th>
                <ng-template *ngTemplateOutlet="containsFilter; context: { $implicit: fields.designId }" />
            </th>
            <th>
                <ng-template *ngTemplateOutlet="containsFilter; context: { $implicit: fields.name }" />
            </th>
            <th>
                <p-calendar
                    #dateFilter
                    appendTo="body"
                    [showClear]="true"
                    (onInput)="dt.filter(dateFilter.value, fields.createdAt, matchModes.DATE_IS)"
                    (onClear)="dt.filter(dateFilter.value, fields.createdAt, matchModes.DATE_IS)"
                    (onSelect)="dt.filter(dateFilter.value, fields.createdAt, matchModes.DATE_IS)"
                ></p-calendar>
            </th>
            <th>
                <ng-template *ngTemplateOutlet="containsFilter; context: { $implicit: fields.createdBy }" />
            </th>
            <th>
                <ng-template *ngTemplateOutlet="containsFilter; context: { $implicit: fields.projectName }" />
            </th>
            <th>
                <ng-template *ngTemplateOutlet="containsFilter; context: { $implicit: fields.projectState }" />
            </th>
            <th>
                <p-calendar
                    #dateFilter
                    appendTo="body"
                    [showClear]="true"
                    (onInput)="dt.filter(dateFilter.value, fields.releasedAt, matchModes.DATE_IS)"
                    (onClear)="dt.filter(dateFilter.value, fields.releasedAt, matchModes.DATE_IS)"
                    (onSelect)="dt.filter(dateFilter.value, fields.releasedAt, matchModes.DATE_IS)"
                ></p-calendar>
            </th>
            <th>
                <ng-template *ngTemplateOutlet="containsFilter; context: { $implicit: fields.releasedBy }" />
            </th>
            <th>
                <p-columnFilter type="boolean" [field]="fields.released"></p-columnFilter>
            </th>
            <th>
                <ng-template *ngTemplateOutlet="containsFilter; context: { $implicit: fields.partNumber }" />
            </th>
            <th>
                <ng-template *ngTemplateOutlet="containsFilter; context: { $implicit: fields.description }" />
            </th>
            <th></th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-design>
        <tr>
            <td>
                <com-design-menu [design]="design" (designChange)="refresh()" />
            </td>
            <td>{{ design.designId }}</td>
            <td>{{ design.name }}</td>
            <td>{{ design.createdAt | date }}</td>
            <td>{{ design.createdBy.name }}</td>
            <td>{{ design.projectName }}</td>
            <td>{{ design.projectState }}</td>
            <td>{{ design.releasedAt | date }}</td>
            <td>{{ design.releasedBy?.name }}</td>
            <td>{{ design.released | boolText : releasedText.yes : releasedText.no }}</td>
            <td>{{ design.partNumber }}</td>
            <td class="break-word white-space-normal">{{ design.description }}</td>
            <td>
                <p-tableCheckbox [value]="design"></p-tableCheckbox>
            </td>
        </tr>
    </ng-template>
</p-table>
