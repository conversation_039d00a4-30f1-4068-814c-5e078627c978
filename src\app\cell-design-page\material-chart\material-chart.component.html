<p-card>
    <div *ngIf="loading; else widgets" class="flex justify-content-center">
        <p-progressSpinner />
    </div>

    <ng-template #widgets>
        <p-chart *ngIf="data" height="40rem" [type]="lineType" [data]="data" [options]="options" />
        <p-table
            *ngIf="data"
            [columns]="data.headers"
            [value]="groupedDatasets"
            [tableStyle]="{ 'min-width': '50rem' }"
        >
            <ng-template pTemplate="header" let-columns>
                <tr>
                    <th></th>
                    <th *ngFor="let header of columns">
                        {{ header }}
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-rowData let-columns="columns">
                <tr>
                    <td>
                        <div class="flex">
                            <span
                                class="legend-line-indicator m-2"
                                [ngStyle]="{ '--line-color': rowData[0].borderColor ?? 'var(--primary-color)' }"
                            ></span>
                            {{ rowData[0].description }}
                        </div>
                    </td>
                    <td *ngFor="let row of rowData" class="content-center">
                        <p-checkbox
                            [(ngModel)]="datasetVisibilities[row.index]"
                            [ngModelOptions]="{ standalone: true }"
                            (ngModelChange)="toggleDataset(row.index)"
                            [disabled]="row.data == null"
                            [binary]="true"
                        ></p-checkbox>
                    </td>
                </tr>
            </ng-template>
        </p-table>

        <div *ngIf="annotations && annotations.length > 0" class="flex flex-wrap mt-3">
            <div *ngFor="let annotation of annotations" class="flex m-auto">
                <span class="legend-line-indicator m-1" [ngStyle]="{ '--line-color': annotation.color }"></span>
                {{ annotation.name }}
            </div>
        </div>
    </ng-template>
</p-card>
