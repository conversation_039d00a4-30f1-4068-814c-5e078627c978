@use "../../../assets/styles/variables.scss" as var;

.css-grid {
    display: grid;
    justify-content: space-evenly;
    align-content: space-evenly;
    align-items: center;
    row-gap: 0.25rem;
    column-gap: 0.5rem;
    grid-auto-rows: minmax(2.5rem, auto);

    & .col-span-2 {
        grid-column: span 2;
    }

    & hr {
        grid-column: 1 / -1;
        margin: 0;
    }
}

@media (max-width: 991px) {
    .css-grid {
        grid-template-columns: 1fr 1fr;

        & .col-span-2.shrink-col {
            grid-column: span 1;
        }

        &.shrink-row {
            grid-auto-rows: minmax(1rem, auto);
        }
    }
}

@media (min-width: 992px) {
    .css-grid {
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }
}

.css-grid.grid-3-col {
    grid-template-columns: 1fr 1fr 1fr;

    &.colored {
        > :nth-child(2),
        :nth-child(4n + 2) {
            color: var.$logoPrimeColor;
        }

        > :nth-child(3),
        :nth-child(4n + 3) {
            color: var.$logoLightSecondaryColor;
        }
    }
}
