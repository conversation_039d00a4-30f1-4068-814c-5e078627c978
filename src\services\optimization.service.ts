import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';
import { CellDesignWithId } from './cell-design.service';
import { MaterialLineChartDataset } from './cell-design-metrics.service';

export type DropdownSettings = {
    objectiveVariables: ObjectiveVariable[];
};

type DropdownOption = { id: string; name: string; unit: string };

export type ObjectiveVariable = {
    objective: DropdownOption;
    algorithms: DropdownOption[];
    stopCriterions: DropdownOption[];
    variables: DropdownOption[];
    optimizationTarget: { id: string; name: string };
};

export interface OptimizationVariable {
    id: string;
    minValue?: number;
    maxValue?: number;
    stepSize?: number;
    unit: string;
}

export enum OptimizationAlgorithmEnum {
    bayesian = 'bayesian',
    brute = 'brute',
}

export type OptimizationSettings = {
    variables: OptimizationVariable[];
    objective: string;
    algorithm: string;
    stopCriterionType?: string;
    stopCriterionValue?: number;
    populationSize?: number;
    optimizationTarget: string;
    numInitialPoints?: number;
    numIterations?: number;
};

export enum OptimizationStatusEnum {
    complete = 'complete',
    pending = 'pending',
    error = 'error',
}

interface OptimizationVariableWithData extends OptimizationVariable {
    name: string;
    unit: string;
    original: number;
    optimal: number;
}

export interface CellOptimization {
    variables: OptimizationVariableWithData[];
    objective: { id: string; name: string; unit: string };
    algorithm: { id: string; name: string };
    stopCriterionType?: string;
    stopCriterionValue?: number;
    populationSize?: number;
    chart: OptimizationChart;
    status: OptimizationStatusEnum;
    numInitialPoints?: number;
    numIterations?: number;
    outcome: {
        optimizedValue: number;
        originalValue: number;
        percentageImprovement: number;
    };
    optimizationOutcome: OptimizationOutcome[];
    progress: number;
    createdAt: string;
}

type OptimizationOutcome = {
    id: string;
    name: string;
    optimizedValue: string | null;
    originalValue: string | null;
    percentageOptimization: number | null;
    unit: string;
};
export interface OptimizationChart {
    datasets: MaterialLineChartDataset[];
}

@Injectable({
    providedIn: 'root',
})
export class OptimizationService {
    private _apiUrl = `${this._config.data.baseUrl}/api/optimization/`;

    private constructor(private _config: ConfigService, private _http: HttpClient) {}

    public getOptimizationOptions(): Observable<ObjectiveVariable[]> {
        return this._http.get<ObjectiveVariable[]>(`${this._config.data.baseUrl}/api/optimization-settings/`);
    }

    public getOptimizationData(optimizationId: string): Observable<CellOptimization> {
        return this._http.get<CellOptimization>(`${this._apiUrl}${optimizationId}`);
    }

    public createOptimization(
        design: CellDesignWithId,
        optimizationSettings: OptimizationSettings
    ): Observable<CellDesignWithId> {
        const payload = { cellDesign: design, optimization: optimizationSettings };

        return this._http.post<CellDesignWithId>(this._apiUrl, payload);
    }
}
