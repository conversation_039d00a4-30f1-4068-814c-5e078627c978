// Allow some console errors since we're in startup
/* eslint-disable no-console */
/// <reference types="@angular/localize" />

import { registerLocaleData } from '@angular/common';
import { loadTranslations } from '@angular/localize';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { PRIME_TRANSLATIONS } from './app/prime-ng';
import { getLocale, importLocale, setLocale } from './utils/i18n';

const locale = getLocale();
setLocale(locale, sessionStorage);

Promise.all([fetch(`assets/i18n/${locale}.json`), importLocale(locale)])
    .then((responses) => {
        if (!responses[0].ok) {
            throw new Error(`HTTP error ${responses[0].status}`);
        }

        const moduleLocale = responses[1].default[0];
        registerLocaleData(responses[1].default, moduleLocale);

        return responses[0].json();
    })
    .then((result) => {
        loadTranslations(result.translations);

        platformBrowserDynamic([{ provide: PRIME_TRANSLATIONS, useValue: result.primeng }])
            .bootstrapModule(AppModule)
            .catch((err) => console.error(err));
    })
    .catch((error) => {
        console.error('Unable to load locale data. Continuing with default.', error);

        platformBrowserDynamic([{ provide: PRIME_TRANSLATIONS, useValue: null }])
            .bootstrapModule(AppModule)
            .catch((err) => console.error(err));
    });
