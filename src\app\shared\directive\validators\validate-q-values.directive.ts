import { Directive, Input } from '@angular/core';
import { FormGroup, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';
import { isNullish } from '@com/utils/object';

export const validateQValuesMinimumError = 'ValidateQValuesMinimumError';
export const validateQValuesComparisonError = 'ValidateQValuesComparisonError';

@Directive({
    selector: '[comValidateQValuesDirective]',
    providers: [{ provide: NG_VALIDATORS, useExisting: ValidateQValuesDirective, multi: true }],
})
export class ValidateQValuesDirective implements Validator {
    @Input()
    public minimumValue: number = 150;

    @Input()
    public qRevControlName: string = 'cathode_q_aim';

    @Input()
    public q1stControlName: string = 'cathode_q_aim_1';

    public validate(group: FormGroup): ValidationErrors | null {
        const qRevControl = group.get(this.qRevControlName);
        const q1stControl = group.get(this.q1stControlName);

        if (!qRevControl || !q1stControl) {
            return null;
        }

        const qRevValue = qRevControl.value;
        const q1stValue = q1stControl.value;
        const errors: ValidationErrors = {};

        if (!isNullish(qRevValue) && qRevValue < this.minimumValue) {
            errors[validateQValuesMinimumError] = { field: 'qRev', value: qRevValue };
        }

        if (!isNullish(q1stValue) && q1stValue < this.minimumValue) {
            errors[validateQValuesMinimumError] = { field: 'q1st', value: q1stValue };
        }

        if (!isNullish(qRevValue) && !isNullish(q1stValue) && q1stValue <= qRevValue) {
            errors[validateQValuesComparisonError] = {
                qrev: qRevValue,
                q1st: q1stValue,
            };
        }

        return Object.keys(errors).length > 0 ? errors : null;
    }
}
