import { Directive, Host, Inject, LOCALE_ID, Optional, Self } from '@angular/core';
import { InputNumber } from 'primeng/inputnumber';

@Directive({ selector: '[comCustomizeInput]' })
export class CustomizePrimeInputDirective {
    public constructor(
        @Inject(LOCALE_ID) private _locale: string,
        @Host() @Self() @Optional() private _inputNumber?: InputNumber
    ) {
        if (this._inputNumber) {
            this._inputNumber.locale = this._locale;

            // Disable grouping/thousands separators since
            // Inputs with suffix, minFractionDigits = 0 and grouping are bugged
            // I.e. a user can press '1' four times, but input value will be '11111'
            this._inputNumber.useGrouping = false;
        }
    }
}
