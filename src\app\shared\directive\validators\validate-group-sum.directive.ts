import { Directive, Input } from '@angular/core';
import { FormGroup, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';

export const validateGroupSumErrorActualNumber = 'ValidateGroupSumErrorActualNumber';

@Directive({
    selector: '[comValidateGroupSumDirective]',
    providers: [{ provide: NG_VALIDATORS, useExisting: ValidateGroupSumDirective, multi: true }],
})
export class ValidateGroupSumDirective implements Validator {
    @Input()
    public desiredSum: number;
    @Input()
    public includedControlNames: string[] | undefined;
    @Input() public skipValidation: boolean = false;

    public validate(group: FormGroup): ValidationErrors | null {
        let actualSum = 0;

        if (this.skipValidation) {
            return null;
        }

        for (const controlName in group.controls) {
            let isControlIncluded = true;

            if (!!this.includedControlNames) {
                isControlIncluded = this.includedControlNames.some((includedName) => {
                    return controlName.includes(includedName);
                });
            }

            if (isControlIncluded) {
                actualSum += group.get(controlName)?.value;
            }
        }

        return this.desiredSum === actualSum ? null : { [validateGroupSumErrorActualNumber]: actualSum };
    }
}
