import { Component, Input, OnInit } from '@angular/core';
import { <PERSON><PERSON>ontainer, NgModelGroup } from '@angular/forms';
import { MessageService } from 'primeng/api';

import { BalancingMetrics, CellDesignMetrics } from '@com/services/cell-design-metrics.service';
import { CellDesign } from '@com/services/cell-design.service';
import { PatchCellDesignFromMetrics } from '@com/app/cell-design-page/types';
import { NG_MODEL_GROUP_NAMES, NG_MODEL_MAIN_SECTION_NAMES } from '@com/app/cell-design-page/const';
import { ToastPositionEnum, ToastSeverityEnum } from '@com/app/prime-ng';
import { deepCloneDto } from '@com/utils/object';
import { LINE_CHART_OPTIONS } from '@com/const';

const CONTROL_NAMES = {
    npRatioFirst: 'balancing_np_ratio_first',
    uMin: 'balancing_u_min',
    uMax: 'balancing_u_max',
} as const;

type BalancingDesignPatch = {
    [NG_MODEL_MAIN_SECTION_NAMES.chemicalDesign]: {
        [NG_MODEL_GROUP_NAMES.balancing]: {
            [CONTROL_NAMES.uMax]: number;
            [CONTROL_NAMES.uMin]: number;
        };
    };
};

@Component({
    selector: 'com-balancing-editor',
    templateUrl: './balancing-editor.component.html',

    // this is important for the change detection to work across components
    // makes this component use the same NgForm as the parent component
    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],
})
export class BalancingEditorComponent implements PatchCellDesignFromMetrics<BalancingDesignPatch>, OnInit {
    @Input()
    public design: CellDesign | null;

    @Input()
    public loading: boolean;

    @Input()
    public set metrics(value: BalancingMetrics | undefined) {
        if (value?.warning) {
            this._messageService.clear(ToastPositionEnum.topCenter);
            this._messageService.add({
                key: ToastPositionEnum.topCenter,
                severity: ToastSeverityEnum.warning,
                summary: $localize`:@@common.warning:Achtung`,
                detail: value.warning,
            });
        }

        this._metrics = value;

        if (value) {
            this.createDefaultOptions();
        }
    }

    public get metrics(): BalancingMetrics | undefined {
        return this._metrics;
    }

    public readonly controlNames = CONTROL_NAMES;

    public readonly maxDigits = 20;

    // chart.js have the chart options defined as any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public options: any = {};

    private _metrics: BalancingMetrics | undefined;

    public constructor(private _messageService: MessageService) {}

    public ngOnInit(): void {
        this.createDefaultOptions();
    }

    public getDesignPatch(metrics: CellDesignMetrics): BalancingDesignPatch {
        const patch = {
            [NG_MODEL_MAIN_SECTION_NAMES.chemicalDesign]: {
                [NG_MODEL_GROUP_NAMES.balancing]: {
                    // Round to PrimeNG input prescision to avoid rounding on blur and triggering valueChanged
                    [CONTROL_NAMES.uMax]: parseFloat(metrics.balancing.uMax.toFixed(this.maxDigits)),
                    [CONTROL_NAMES.uMin]: parseFloat(metrics.balancing.uMin.toFixed(this.maxDigits)),
                },
            },
        };

        return patch;
    }

    private createDefaultOptions(): void {
        this.options = {
            ...deepCloneDto(LINE_CHART_OPTIONS.rootOptions),
            plugins: deepCloneDto(LINE_CHART_OPTIONS.plugins),
            scales: deepCloneDto(LINE_CHART_OPTIONS.scales),
        };

        this.options.scales.x.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);
        this.options.scales.x.title.text = $localize`:@@axisTitle.capacityCathode: Capacity / mAh/g(CAM)`;

        this.options.scales.y.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);
        this.options.scales.y.title.text = $localize`:@@axisTitle.cellPotential: Cell potential / V`;

        if (this.metrics) {
            this.options.plugins.annotation = {
                annotations: this.metrics?.chartAnnotations,
            };
        }
    }
}
