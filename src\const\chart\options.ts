// TODO Replace hardcoded value with value gotten from ccs font variables
const axisTickFontSize = 16;
const axisTitleFontSize = 20;

export const LINE_CHART_OPTIONS = {
    rootOptions: {
        pointStyle: false,
        borderWidth: 1.5,
        animation: false,
    },
    plugins: {
        legend: {
            display: false,
        },
    },
    scales: {
        x: {
            type: 'linear',
            ticks: { font: { size: axisTickFontSize } },
        },
        y: {
            ticks: { font: { size: axisTickFontSize } },
        },
    },
    axisTitle: {
        display: true,
        text: '',
        font: { size: axisTitleFontSize },
    },
} as const;

export const BAR_CHART_OPTIONS = {
    rootOptions: {
        animation: false,
    },
    horizontal: {
        indexAxis: 'y',
    },
    bottomLegendNoClick: {
        legend: {
            position: 'bottom',
            onClick: null,
        },
    },
    stackedScales: {
        x: {
            stacked: true,
        },
        y: {
            stacked: true,
        },
    },
} as const;
