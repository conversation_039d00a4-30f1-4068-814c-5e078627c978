<div *ngIf="!design || !selectedOptimizationSettings" class="p-4">
    <span i18n="@@createOptimization.noOptimizationMessage"
        >Optimierung der Anodenmaterialm ischung nicht verfügbar.</span
    >
</div>

<div class="grid align-items-stretch">
    <div *ngIf="design && selectedOptimizationSettings && objectiveVariableSettings" class="col-12 xl:col-8 pb-5">
        <com-design-meta-editor
            [design]="design"
            [(dialogVisible)]="dialogVisible"
            [dialogType]="MetaEditorTypeEnum.optimize"
            [optimizationSettings]="selectedOptimizationSettings"
        ></com-design-meta-editor>

        <p-panel>
            <ng-template pTemplate="header">
                <strong i18n="@@createOptimization.objectiveFunction" class="text-xl">Zielfunktion</strong>
            </ng-template>
            <div class="grid align-items-center">
                <label
                    i18n="@@createOptimization.optimizationObjective"
                    for="upperLimit"
                    class="col-12 sm:col-4 lg:col-4"
                    >Optimierungsziel</label
                >
                <p-dropdown
                    name="optimizationObjective"
                    [inputId]="'optimizationObjective'"
                    [options]="optimizationObjectiveOptions"
                    [(ngModel)]="selectedOptimizationSettings.objective"
                    (ngModelChange)="updateOptionsBasedOnObjective()"
                    optionLabel="name"
                    optionValue="id"
                    class="col-12 lg:col-5 p-fluid mb-2"
                    [ngClass]="{ 'ng-invalid': objectivesValidationErrors !== null }"
                ></p-dropdown>
                <small
                    i18n="@@createOptimization.cellCostValidation"
                    *ngIf="objectivesValidationErrors?.objectiveCostInvalid"
                    class="p-error mb-4 col-12"
                >
                    Keine Zellkosten verfügbar. Bitte lege alle Preise im BOM-Editor fest.
                </small>
                <small
                    i18n="@@createOptimization.swellingValidation"
                    *ngIf="objectivesValidationErrors?.objectiveSwellingInvalid"
                    class="p-error mb-4 col-12"
                >
                    Die Optimierung des Swelling-Ziels ist nicht möglich. Bitte aktiviere Swelling.
                </small>
            </div>

            <div class="grid align-items-center">
                <label i18n="@@createOptimization.optimizationTarget" for="upperLimit" class="col-12 sm:col-4 lg:col-4"
                    >Optimierungsfokus</label
                >
                <div class="col-12 md:col-8 p-fluid pl-2">
                    {{ objectiveVariableSettings.optimizationTarget.name }}
                </div>
            </div>

            <div class="grid align-items-center">
                <label
                    i18n="@@createOptimization.optimizationAlgorithm"
                    for="upperLimit"
                    class="col-12 sm:col-4 lg:col-4"
                    >Optimierungsalgorithmus</label
                >
                <p-dropdown
                    name="optimizationOAlgorithm"
                    [inputId]="'optimizationOAlgorithm'"
                    [options]="optimizationOAlgorithmOptions"
                    [(ngModel)]="selectedOptimizationSettings.algorithm"
                    (ngModelChange)="calculateNumberOfIterations()"
                    optionLabel="name"
                    optionValue="id"
                    class="col-12 lg:col-5 p-fluid"
                ></p-dropdown>
            </div>
        </p-panel>

        <p-panel class="mb-4">
            <ng-template pTemplate="header">
                <strong i18n="@@createOptimization.parameterSet" class="text-xl">Parametersatz</strong>
            </ng-template>
            <div class="grid">
                <span i18n="@@createOptimization.optimizationVariables" class="col-6">OptimierungSvariablen</span>
                <label i18n="@@createOptimization.lowerLimit" for="lowerLimit" class="col-2">Unteres Limit</label>
                <label i18n="@@createOptimization.upperLimit" for="upperLimit" class="col-2">Oberes Limit</label>
                <label
                    *ngIf="selectedOptimizationSettings.algorithm !== OptimizationAlgorithmEnum.bayesian"
                    i18n="@@createOptimization.stepSize"
                    for="stepSize"
                    class="col-2"
                    >Limit Schrittgröße</label
                >

                <ng-container *ngFor="let variable of selectedOptimizationSettings.variables; let i = index">
                    <div class="col-6 flex">
                        <p-button
                            icon="pi pi-minus"
                            (onClick)="removeOptimizationVariable(i)"
                            class="mr-0 lg:mr-2"
                            [disabled]="selectedOptimizationSettings.variables.length === 1"
                        ></p-button>
                        <div class="col-11 p-fluid pt-0">
                            <p-dropdown
                                [name]="'optimizationVariable' + i"
                                [inputId]="'optimizationVariable' + i"
                                [options]="optimizationVariablesOptions"
                                [(ngModel)]="selectedOptimizationSettings.variables[i].id"
                                (ngModelChange)="onVariableChange($event)"
                                optionLabel="name"
                                optionValue="id"
                            ></p-dropdown>
                            <small
                                i18n="@@createOptimization.anodeWeightValidation"
                                *ngIf="optimizationVariablesValidationErrors.get(variable.id)?.anodeWeightInvalid"
                                class="p-error"
                                >Die Optimierung des Anodengewichts ist nicht möglich. Nur Mischungen mit Material der
                                Gruppe 14 SCC55 sind zulässig.
                            </small>
                            <small
                                i18n="@@createOptimization.cathodeWeightValidation"
                                *ngIf="optimizationVariablesValidationErrors.get(variable.id)?.cathodeWeightInvalid"
                                class="p-error"
                            >
                                Eine Optimierung des Kathodengewichts ist nicht möglich. Um dies zu ermöglichen, müssen
                                Sie Kathodenmaterialien mischen.
                            </small>
                        </div>
                    </div>

                    <div class="col-2 p-fluid">
                        <p-inputNumber
                            [inputId]="'lowerLimit' + i"
                            [name]="'lowerLimit' + i"
                            [(ngModel)]="variable.minValue"
                            (ngModelChange)="calculateNumberOfIterations(); updateVariableValidation(variable)"
                            mode="decimal"
                            [suffix]="
                                selectedOptimizationSettings.variables[i].unit
                                    ? ' ' + selectedOptimizationSettings.variables[i].unit
                                    : ''
                            "
                            [min]="0"
                            [required]="true"
                            [ngClass]="{
                                'ng-invalid': optimizationVariablesValidationErrors.get(variable.id)?.lowerLimitInvalid
                            }"
                        ></p-inputNumber>
                        <small
                            i18n="@@createOptimization.lowerLimitValidation"
                            *ngIf="optimizationVariablesValidationErrors.get(variable.id)?.lowerLimitInvalid"
                            class="p-error"
                        >
                            Minimalwert muss unter Höchstwert liegen
                        </small>
                        <small
                            i18n="@@swelling.fieldIsRequired"
                            *ngIf="optimizationVariablesValidationErrors.get(variable.id)?.missingMinValue"
                            class="p-error"
                        >
                            Feld ist erforderlich.
                        </small>
                    </div>

                    <div class="col-2 p-fluid">
                        <p-inputNumber
                            comCustomizeInput
                            [inputId]="'upperLimit' + i"
                            [name]="'upperLimit' + i"
                            [(ngModel)]="selectedOptimizationSettings.variables[i].maxValue"
                            (ngModelChange)="calculateNumberOfIterations(); updateVariableValidation(variable)"
                            mode="decimal"
                            [suffix]="
                                selectedOptimizationSettings.variables[i].unit
                                    ? ' ' + selectedOptimizationSettings.variables[i].unit
                                    : ''
                            "
                            [min]="0"
                            [required]="true"
                        ></p-inputNumber>
                        <small
                            i18n="@@swelling.fieldIsRequired"
                            *ngIf="optimizationVariablesValidationErrors.get(variable.id)?.missingMaxValue"
                            class="p-error"
                        >
                            Feld ist erforderlich.
                        </small>
                    </div>

                    <div
                        *ngIf="selectedOptimizationSettings.algorithm !== OptimizationAlgorithmEnum.bayesian"
                        class="col-2 p-fluid"
                    >
                        <p-inputNumber
                            [inputId]="'stepSize' + i"
                            [name]="'stepSize' + i"
                            [(ngModel)]="variable.stepSize"
                            (ngModelChange)="calculateNumberOfIterations(); updateVariableValidation(variable)"
                            mode="decimal"
                            [suffix]="
                                selectedOptimizationSettings.variables[i].unit
                                    ? ' ' + selectedOptimizationSettings.variables[i].unit
                                    : ''
                            "
                            [min]="0.001"
                            [minFractionDigits]="2"
                            [required]="true"
                            [ngClass]="{
                                'ng-invalid': optimizationVariablesValidationErrors.get(variable.id)?.stepSizeInvalid
                            }"
                        ></p-inputNumber>
                        <small
                            i18n="@@createOptimization.stepSizeValidation"
                            *ngIf="optimizationVariablesValidationErrors.get(variable.id)?.stepSizeInvalid"
                            class="p-error"
                        >
                            Schrittgröße muss kleiner sein als die Differenz zwischen Ober und Untergrenze
                        </small>
                        <small
                            i18n="@@swelling.fieldIsRequired"
                            *ngIf="optimizationVariablesValidationErrors.get(variable.id)?.missingStepSizeValue"
                            class="p-error"
                        >
                            Feld ist erforderlich.
                        </small>
                    </div>
                </ng-container>
            </div>
            <p-button
                icon="pi pi-plus"
                (onClick)="addOptimizationVariable()"
                [disabled]="selectedOptimizationSettings.variables.length >= optimizationVariablesOptions.length"
            ></p-button>
        </p-panel>

        <p-panel *ngIf="selectedOptimizationSettings.algorithm === OptimizationAlgorithmEnum.bayesian">
            <ng-template pTemplate="header">
                <strong i18n="@@createOptimization.advancedSettings" class="text-xl">Erweiterte Einstellungen</strong>
            </ng-template>
            <div class="grid align-items-center">
                <label i18n="@@createOptimization.numInitialPoints" class="col-12 sm:col-3 lg:col-4"
                    >Anzahl der Anfangspunkte</label
                >
                <p-inputNumber
                    [(ngModel)]="selectedOptimizationSettings.numInitialPoints"
                    (ngModelChange)="calculateNumberOfIterations()"
                    [min]="0"
                    [useGrouping]="false"
                    [required]="true"
                    class="col-12 md:col-3 lg:col-3 p-fluid"
                ></p-inputNumber>
            </div>
            <div class="grid align-items-center">
                <label i18n="@@createOptimization.numIterations" for="upperLimit" class="col-12 sm:col-3 lg:col-4"
                    >Anzahl der Iterationen</label
                >
                <p-inputNumber
                    [(ngModel)]="selectedOptimizationSettings.numIterations"
                    (ngModelChange)="calculateNumberOfIterations()"
                    [min]="0"
                    [useGrouping]="false"
                    [required]="true"
                    class="col-12 md:col-3 lg:col-3 p-fluid"
                ></p-inputNumber>
            </div>
        </p-panel>
        <div *ngIf="messages" class="col-12 xl:col-8 pb-5">
            <p-messages
                [(value)]="messages"
                [showTransitionOptions]="transitionDuration"
                [hideTransitionOptions]="transitionDuration"
            />
        </div>
        <p-button
            i18n-label="@@createOptimization.createNewOptimizedDesign"
            label="Neues optimiertes Design erstellen"
            (click)="onStartOptimizationClick()"
            [disabled]="areOptimizationSettingsInvalid()"
            class="pl-3"
        ></p-button>
    </div>
</div>
