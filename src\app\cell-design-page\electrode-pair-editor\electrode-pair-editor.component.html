<div *ngIf="loading || !design" class="flex justify-content-center">
    <p-progressSpinner />
</div>

<div *ngIf="design && !loading" class="grid align-items-stretch">
    <div class="col-12 lg:col-6">
        <p-panel i18n-header="@@electrodePair.calendarDates.label" header="Kalenderdaten">
            <div class="field grid">
                <label
                    [for]="controlNames.cathodeCalanderDensity"
                    class="col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@electrodePair.cathodeDensity.input.label"
                >
                    Dichte Kathode
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.cathodeCalanderDensity"
                        [inputId]="controlNames.cathodeCalanderDensity"
                        [(ngModel)]="design.cathodeCalanderDensity"
                        mode="decimal"
                        suffix=" g/cm³"
                        [maxFractionDigits]="maxDigits"
                        [min]="0.5"
                        [required]="true"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label
                    [for]="controlNames.anodeCalanderDensity"
                    class="col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@electrodePair.anodeDensity.input.label"
                >
                    Dichte Anode
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.anodeCalanderDensity"
                        [inputId]="controlNames.anodeCalanderDensity"
                        [(ngModel)]="design.anodeCalanderDensity"
                        mode="decimal"
                        suffix=" g/cm³"
                        [maxFractionDigits]="maxDigits"
                        [min]="0.5"
                        [required]="true"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@electrodePair.proposityCathode.label">
                    Porosität Kathode
                </label>
                <div
                    *ngIf="metrics && metrics.cathodePorosity"
                    class="col-12 xl:col-9 p-fluid pl-2"
                    i18n="@@common.percentageNumber"
                >
                    {{ metrics.cathodePorosity | numberTwoFractionDigits }}&nbsp;%
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@electrodePair.proposityAnode.label">
                    Porosität Anode
                </label>
                <div
                    *ngIf="metrics && metrics.anodePorosity"
                    class="col-12 xl:col-9 p-fluid pl-2"
                    i18n="@@common.percentageNumber"
                >
                    {{ metrics.anodePorosity | numberTwoFractionDigits }}&nbsp;%
                </div>
            </div>
        </p-panel>

        <p-panel
            class="mt-3 block"
            i18n-header="@@electrodePair.coatingAreaLayerThickness.label"
            header="Beschichtungsflächendichte und Schichtdicke"
        >
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@electrodePair.balancingAnodeCathode.label">
                    Balancing (Anode/Kathode)
                </label>
                <div *ngIf="metrics && metrics.balancing" class="col-12 xl:col-9 p-fluid pl-2">
                    {{ metrics.balancing | numberTwoFractionDigits }}
                </div>
            </div>
            <div class="field grid">
                <label
                    [for]="controlNames.cathodeArealCapacity"
                    class="col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@electrodePair.chargeDensityCathode.input.label"
                >
                    Ladungsdichte Kathode
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.cathodeArealCapacity"
                        [inputId]="controlNames.cathodeArealCapacity"
                        [(ngModel)]="design.cathodeArealCapacity"
                        mode="decimal"
                        suffix=" mAh/cm²"
                        [maxFractionDigits]="maxDigits"
                        [min]="1"
                        [required]="true"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@electrodePair.chargeDensityAnode.label">
                    Ladungsdichte Anode
                </label>
                <div
                    *ngIf="metrics && metrics.anodeAreaCapacity"
                    class="col-12 xl:col-9 p-fluid pl-2"
                    i18n="@@common.milliAmperHourPerSquareCentiMeter"
                >
                    {{ metrics.anodeAreaCapacity | numberTwoFractionDigits }}&nbsp;mAh/cm²
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@electrodePair.loadingCathode.label">
                    Beladung Kathode
                </label>
                <div
                    *ngIf="metrics && metrics.cathodeLoading"
                    class="col-12 xl:col-9 p-fluid pl-2"
                    i18n="@@common.milliGramPerSquareCentiMeter"
                >
                    {{ metrics.cathodeLoading | numberTwoFractionDigits }}&nbsp;mg/cm²
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@electrodePair.loadingAnode.label">
                    Beladung Anode
                </label>
                <div
                    *ngIf="metrics && metrics.anodeLoading"
                    class="col-12 xl:col-9 p-fluid pl-2"
                    i18n="@@common.milliGramPerSquareCentiMeter"
                >
                    {{ metrics.anodeLoading | numberTwoFractionDigits }}&nbsp;mg/cm²
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@electrodePair.coatingThicknessCathode.label">
                    Beshichtungsdicke Kathode
                </label>
                <div
                    *ngIf="metrics && metrics.cathodeCoatingThickness"
                    class="col-12 xl:col-9 p-fluid pl-2"
                    i18n="@@common.microMeter"
                >
                    {{ metrics.cathodeCoatingThickness | numberTwoFractionDigits }}&nbsp;µm
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@electrodePair.coatingThicknessAnode.label">
                    Beshichtungsdicke Anode
                </label>
                <div
                    *ngIf="metrics && metrics.anodeCoatingThickness"
                    class="col-12 xl:col-9 p-fluid pl-2"
                    i18n="@@common.microMeter"
                >
                    {{ metrics.anodeCoatingThickness | numberTwoFractionDigits }}&nbsp;µm
                </div>
            </div>
            <div class="field grid">
                <label
                    [for]="controlNames.thicknessAluminiumFoil"
                    class="col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@electrodePair.aluminumFoilThickness.input.label"
                >
                    Dicke Alufolie
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.thicknessAluminiumFoil"
                        [inputId]="controlNames.thicknessAluminiumFoil"
                        [(ngModel)]="design.thicknessAluminiumFoil"
                        mode="decimal"
                        suffix=" µm"
                        [maxFractionDigits]="maxDigits"
                        [min]="0.0001"
                        [required]="true"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label
                    [for]="controlNames.thicknessCopperFoil"
                    class="col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@electrodePair.copperFoilThickness.input.label"
                >
                    Dicke Kupferfolie
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.thicknessCopperFoil"
                        [inputId]="controlNames.thicknessCopperFoil"
                        [(ngModel)]="design.thicknessCopperFoil"
                        mode="decimal"
                        suffix=" µm"
                        [maxFractionDigits]="maxDigits"
                        [min]="0.0001"
                        [required]="true"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label
                    [for]="controlNames.thicknessSeparator"
                    class="col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@electrodePair.separatorThickness.input.label"
                >
                    Dicke Separator
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.thicknessSeparator"
                        [inputId]="controlNames.thicknessSeparator"
                        [(ngModel)]="design.thicknessSeparator"
                        mode="decimal"
                        suffix=" µm"
                        [maxFractionDigits]="maxDigits"
                        [min]="0.0001"
                        [required]="true"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0">
                    <strong i18n="@@electrodePair.cellLayerThickness.label">Dicke Zelllage</strong>
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <strong *ngIf="metrics && metrics.cellLayerThickness" class="ml-1" i18n="@@common.microMeter">
                        {{ metrics.cellLayerThickness | numberTwoFractionDigits }}&nbsp;µm
                    </strong>
                </div>
            </div>
        </p-panel>
    </div>

    <div class="col-12 lg:col-6">
        <p-panel i18n-header="@@electrodePair.electrodeThickness.label" header="Elektrodendicken">
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0">
                    <strong i18n="@@electrodePair.cathodeThickness.label">Dicke Kathode</strong>
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <strong *ngIf="metrics && metrics.cathodeThickness" class="ml-1" i18n="@@common.microMeter">
                        {{ metrics.cathodeThickness | numberTwoFractionDigits }}&nbsp;µm
                    </strong>
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0">
                    <strong i18n="@@electrodePair.anodeThickness.label">Dicke Anode</strong>
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <strong *ngIf="metrics && metrics.anodeThickness" class="ml-1" i18n="@@common.microMeter">
                        {{ metrics.anodeThickness | numberTwoFractionDigits }}&nbsp;µm
                    </strong>
                </div>
            </div>
        </p-panel>

        <p-chart
            class="block pt-5"
            *ngIf="!loading"
            height="30rem"
            [type]="'bar'"
            [data]="{ labels: [''], datasets: metrics?.designDatasets }"
            [options]="options"
        />
    </div>
</div>
