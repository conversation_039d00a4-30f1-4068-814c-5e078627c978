import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';

export enum CellGeometryEnum {
    pouch = 'POUCH',
    prisma = 'PRISMA',
    cylinder = 'CYLINDER',
}

interface CellFormatPropertiesBase {
    coatingWidthCathode: number;
    coatingWidthAnode: number;
    coatingWidthSeparator: number;
    housingThickness: number;
    swellingBuffer: number;
    housingWeight: number | null;
}

export interface CellFormatPouchProperties extends CellFormatPropertiesBase {
    coatingLengthCathode: number;
    coatingLengthAnode: number;
    coatingLengthSeparator: number;
    cellLength: number;
    cellWidth: number;
    cellThickness: number;
}

export interface CellFormatPrismaProperties extends CellFormatPouchProperties {
    hasTwoSubstacks: boolean;
}

export interface CellFormatCylinderProperties extends CellFormatPropertiesBase {
    anodeOverhang: number;
    cellCoreDiameter: number;
    cellDiameter: number;
    cellHeight: number;
    coatingLengthCathode: number | null;
    coatingLengthAnode: number | null;
    coatingLengthSeparator: number | null;
}

interface CellFormat {
    id: string;
    name: string;
}

interface PouchCellFormat extends CellFormat {
    geometry: CellGeometryEnum.pouch;
    defaultProperties: CellFormatPouchProperties;
}

interface PrismaCellFormat extends CellFormat {
    geometry: CellGeometryEnum.prisma;
    defaultProperties: CellFormatPrismaProperties;
}

interface CylinderCellFormat extends CellFormat {
    geometry: CellGeometryEnum.cylinder;
    defaultProperties: CellFormatCylinderProperties;
}

export type CellFormats = PouchCellFormat | PrismaCellFormat | CylinderCellFormat;

export type CellFormatProperties =
    | CellFormatPouchProperties
    | CellFormatPrismaProperties
    | CellFormatCylinderProperties;

@Injectable({ providedIn: 'root' })
export class CellFormatService {
    public constructor(private _config: ConfigService, private readonly _http: HttpClient) {}

    public getAllCellFormats(): Observable<CellFormats[]> {
        return this._http.get<CellFormats[]>(`${this._config.data.baseUrl}/api/cell-format/`);
    }
}
