import { Directive, OnInit, OnDestroy, Input } from '@angular/core';
import { NgModel } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

@Directive({
    selector: '[comConstrainValue][ngModel]',
})
export class ConstrainValueDirective implements OnInit, OnDestroy {
    @Input()
    public set max(value: number) {
        this._max = value;
        this.updateIfOutOfBounds();
    }

    public get max(): number {
        return this._max;
    }

    @Input()
    public set min(value: number) {
        this._min = value;
        this.updateIfOutOfBounds();
    }

    public get min(): number {
        return this._min;
    }

    private _min: number;
    private _max: number;
    private readonly _ngUnsubscribe = new Subject<void>();

    public constructor(private readonly _ngModel: NgModel) {}

    public ngOnInit(): void {
        this.updateIfOutOfBounds();

        if (!this._ngModel.valueChanges) {
            throw 'Control is missing valueChanges';
        }

        this._ngModel.valueChanges.pipe(takeUntil(this._ngUnsubscribe)).subscribe(() => {
            this.updateIfOutOfBounds();
        });
    }

    public ngOnDestroy(): void {
        this._ngUnsubscribe.next();
        this._ngUnsubscribe.complete();
    }

    private updateIfOutOfBounds(): void {
        const value = this._ngModel.model;
        if (this.max != null && value > this.max) {
            // TODO: Remove setTimeout hack..
            setTimeout(() => {
                this._ngModel.update.emit(this.max);
            });
        } else if (this.min != null && value < this.min) {
            setTimeout(() => {
                this._ngModel.update.emit(this.min);
            });
        }
    }
}
