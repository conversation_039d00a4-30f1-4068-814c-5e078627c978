<div *ngIf="loading" class="flex justify-content-center">
    <p-progressSpinner />
</div>

<div *ngIf="design && metrics" class="grid align-items-stretch">
    <ng-template #density let-isMobileRight="isMobileRight"
        ><span i18n="@@common.density" [ngClass]="{ 'density-mobile': isMobileRight }">Dichte</span></ng-template
    >
    <ng-template #dividerSmall><hr class="col-span-4 mobile-only" /></ng-template>

    <div class="col-12 lg:col-6">
        <p-panel>
            <ng-template pTemplate="header">
                <strong i18n="@@common.anode" class="text-xl">Anode</strong>
                <div *ngIf="anodeWeightFormGroup.invalid" class="com-text-error">
                    <span class="error-icon"><i class="pi pi-times-circle"></i></span>
                    <span i18n="@@materials.percentages.error">Die Summe der Prozentsätze muss 100 ergeben</span>
                </div>
            </ng-template>

            <div class="css-grid">
                <ng-container
                    [ngModelGroup]="formGroupNames.anodeWeight"
                    #anodeWeightFormGroup="ngModelGroup"
                    comValidateGroupSumDirective
                    [desiredSum]="desiredSum"
                    [includedControlNames]="[
                        controlNames.anodeActiveMaterialWeight,
                        controlNames.anodeBinderPpaWeight,
                        controlNames.anodeConductiveAdditiveWeight
                    ]"
                >
                    <ng-container>
                        <label
                            i18n="@@materials.activeMaterialWeight.input.label"
                            [htmlFor]="controlNames.anodeActiveMaterialWeight"
                        >
                            Aktivmaterial
                        </label>
                        <div class="flex-group">
                            <p-inputNumber
                                [ngClass]="{
                                    'ng-invalid ng-dirty': anodeWeightFormGroup.invalid
                                }"
                                #anodeActiveMaterial
                                comCustomizeInput
                                [inputId]="controlNames.anodeActiveMaterialWeight"
                                [name]="controlNames.anodeActiveMaterialWeight"
                                [(ngModel)]="design.activeMaterialAnodeWeightPercent"
                                [minFractionDigits]="minFractionDigits"
                                [maxFractionDigits]="maxFractionDigits"
                                suffix=" w%"
                                [min]="0.01"
                                [max]="desiredSum"
                                [required]="true"
                            />
                            <ng-container
                                *ngTemplateOutlet="
                                    addRemoveButtons;
                                    context: {
                                        formGroup: anodeWeightFormGroup,
                                        onAddButtonClick: applyAdditionalAnodeValues,
                                        onRemoveButtonClick: removeAdditionalAnodeValues
                                    }
                                "
                            />
                        </div>
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span>{{ metrics.anode.activeMaterialDensity | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                    </ng-container>

                    <ng-container *ngTemplateOutlet="dividerSmall" />

                    <ng-container>
                        <span class="break-word">
                            {{ lookupMaterial(design.anodeMaterials[0].materialId)?.name }}
                        </span>
                        <span class="ml-1"
                            >{{ metrics.anode.material1WeightPercent | numberTwoFractionDigits }}&nbsp;w%</span
                        >
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span>{{ metrics.anode.material1Density | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                    </ng-container>

                    <ng-container *ngTemplateOutlet="dividerSmall" />

                    <ng-container
                        *ngIf="
                            design.anodeMaterials.length > 1 &&
                            metrics.anode.material2WeightPercent != null &&
                            metrics.anode.material2Density != null
                        "
                    >
                        <span class="break-word">
                            {{ lookupMaterial(design.anodeMaterials[1].materialId)?.name }}
                        </span>
                        <span class="ml-1"
                            >{{ metrics.anode.material2WeightPercent | numberTwoFractionDigits }}&nbsp;w%</span
                        >
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span>{{ metrics.anode.material2Density | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                        <ng-container *ngTemplateOutlet="dividerSmall" />
                    </ng-container>
                    <hr class="hr" />

                    <label
                        class="col-span-4"
                        i18n="@@materials.binderWeight.input.label"
                        [htmlFor]="controlNames.anodeBinderVersion + '0'"
                    >
                        Binder
                    </label>
                    <ng-container *ngFor="let selectedOption of design.anodeBinderMaterials; let i = index">
                        <div class="flex-group mobile-tiny-col-span-2">
                            <p-button
                                icon="pi pi-minus"
                                (onClick)="
                                    removeVersion(
                                        selectedOption.materialId,
                                        anodeBinderMaterialsAllOptions,
                                        design.anodeBinderMaterials
                                    )
                                "
                                [disabled]="design.anodeBinderMaterials.length === 1"
                            ></p-button>
                            <p-dropdown
                                [name]="controlNames.anodeBinderVersion + i"
                                [inputId]="controlNames.anodeBinderVersion + i"
                                [options]="anodeBinderMaterialsAllOptions"
                                [(ngModel)]="selectedOption.materialId"
                                optionLabel="name"
                                optionValue="id"
                                (onChange)="
                                    changeBinderVersion(anodeBinderMaterialsAllOptions, design.anodeBinderMaterials)
                                "
                                class="col-12 xl:col-9 p-fluid"
                            ></p-dropdown>
                        </div>
                        <div class="flex-group mobile-tiny-col-span-2">
                            <p-inputNumber
                                [ngClass]="{
                                    'ng-invalid ng-dirty': anodeWeightFormGroup.invalid
                                }"
                                comCustomizeInput
                                [inputId]="controlNames.anodeBinderPpaWeight + i"
                                [name]="controlNames.anodeBinderPpaWeight + i"
                                [(ngModel)]="selectedOption.weightPercent"
                                [minFractionDigits]="minFractionDigits"
                                [maxFractionDigits]="maxFractionDigits"
                                mode="decimal"
                                suffix=" w%"
                                [min]="0"
                                [required]="true"
                            />
                            <ng-container
                                *ngTemplateOutlet="
                                    addRemoveButtons;
                                    context: {
                                        formGroup: anodeWeightFormGroup,
                                        onAddButtonClick: applyAdditionalAnodeValues,
                                        onRemoveButtonClick: removeAdditionalAnodeValues,
                                        selectedOption: selectedOption
                                    }
                                "
                            />
                        </div>
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span
                            >{{
                                anodeBinderDensities.get(selectedOption.materialId) | numberTwoFractionDigits
                            }}&nbsp;g/cm³</span
                        >
                    </ng-container>
                    <ng-container>
                        <p-button
                            icon="pi pi-plus"
                            (onClick)="addVersion(anodeBinderMaterialsAllOptions, design.anodeBinderMaterials)"
                            *ngIf="anodeBinderMaterialsAllOptions.length > design.anodeBinderMaterials.length"
                        ></p-button>
                    </ng-container>
                    <hr class="hr" />

                    <label
                        class="col-span-4"
                        i18n="@@materials.conductiveAdditive.input.label"
                        [htmlFor]="controlNames.anodeConductiveAdditiveVersion + '0'"
                    >
                        Conductive Additive Materialien
                    </label>

                    <ng-container *ngFor="let selectedOption of design.anodeConductiveAdditiveMaterials; let i = index">
                        <div class="flex-group mobile-tiny-col-span-2">
                            <p-button
                                icon="pi pi-minus"
                                (onClick)="
                                    removeVersion(
                                        selectedOption.materialId,
                                        anodeConductiveMaterialsAllOptions,
                                        design.anodeConductiveAdditiveMaterials
                                    )
                                "
                                [disabled]="design.anodeConductiveAdditiveMaterials.length === 1"
                            ></p-button>
                            <p-dropdown
                                [name]="controlNames.anodeConductiveAdditiveVersion + i"
                                [inputId]="controlNames.anodeConductiveAdditiveVersion + i"
                                [options]="anodeConductiveMaterialsAllOptions"
                                [(ngModel)]="selectedOption.materialId"
                                optionLabel="name"
                                optionValue="id"
                                (onChange)="
                                    changeBinderVersion(
                                        anodeConductiveMaterialsAllOptions,
                                        design.anodeConductiveAdditiveMaterials
                                    )
                                "
                                class="col-12 xl:col-9 p-fluid"
                            ></p-dropdown>
                        </div>
                        <div class="flex-group mobile-tiny-col-span-2">
                            <p-inputNumber
                                [ngClass]="{
                                    'ng-invalid ng-dirty': anodeWeightFormGroup.invalid
                                }"
                                comCustomizeInput
                                [inputId]="controlNames.anodeConductiveAdditiveWeight + i"
                                [name]="controlNames.anodeConductiveAdditiveWeight + i"
                                [(ngModel)]="selectedOption.weightPercent"
                                [minFractionDigits]="minFractionDigits"
                                [maxFractionDigits]="maxFractionDigits"
                                mode="decimal"
                                suffix=" w%"
                                [min]="0"
                                [required]="true"
                            />
                            <ng-container
                                *ngTemplateOutlet="
                                    addRemoveButtons;
                                    context: {
                                        formGroup: anodeWeightFormGroup,
                                        onAddButtonClick: applyAdditionalAnodeValues,
                                        onRemoveButtonClick: removeAdditionalAnodeValues,
                                        selectedOption
                                    }
                                "
                            />
                        </div>
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span
                            >{{
                                anodeConductiveDensities.get(selectedOption.materialId) | numberTwoFractionDigits
                            }}&nbsp;g/cm³</span
                        >
                    </ng-container>

                    <ng-container>
                        <p-button
                            icon="pi pi-plus"
                            (onClick)="
                                addVersion(anodeConductiveMaterialsAllOptions, design.anodeConductiveAdditiveMaterials)
                            "
                            *ngIf="
                                anodeConductiveMaterialsAllOptions.length >
                                design.anodeConductiveAdditiveMaterials.length
                            "
                        ></p-button>
                    </ng-container>
                </ng-container>
                <hr class="hr" />

                <ng-container>
                    <label
                        class="col-span-4"
                        i18n="@@materials.currentCollector.input.label"
                        [htmlFor]="controlNames.copperCurrentCollector"
                    >
                        Stromableiter
                    </label>
                    <div class="flex-group mobile-tiny-col-span-2">
                        <p-dropdown
                            [name]="controlNames.copperCurrentCollector"
                            [inputId]="controlNames.copperCurrentCollector"
                            [options]="copperCurrentCollectorMaterialsAllOptions"
                            [(ngModel)]="design.copperCurrentCollectorMaterialId"
                            optionLabel="name"
                            optionValue="id"
                            class="col-12 xl:col-9 p-fluid"
                        ></p-dropdown>
                    </div>
                    <span></span>
                    <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                    <span>{{ metrics.copperDensity | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                </ng-container>
                <hr class="hr" />

                <ng-container *ngTemplateOutlet="dividerSmall" />

                <h6 class="col-span-4" i18n="@@materials.weightAfterPrelithiation.header">
                    Gewichtsprozente nach Prälithiierung
                </h6>

                <ng-container>
                    <span class="col-span-2 break-word" i18n="@@materials.prelithiationProcess.label">
                        Prälithiierungsprozess
                    </span>
                    <span class="col-span-2 flex justify-content-between">
                        <span i18n="@@materials.nanoscale.label">Nanoscale</span>
                        <p-inputSwitch
                            [inputId]="controlNames.anodeisGroup14"
                            [name]="controlNames.anodeisGroup14"
                            [(ngModel)]="design.prelithiationProcess"
                            trueValue="group14"
                            falseValue="nanoscale"
                        ></p-inputSwitch>
                        <span i18n="@@materials.group14.label">Group 14</span>
                    </span>
                </ng-container>

                <ng-container *ngTemplateOutlet="dividerSmall" />

                <ng-container>
                    <span i18n="@@materials.lithiumWeight.label">Lithium</span>
                    <span> {{ metrics.prelithiation.lithiumWeightPercent | numberTwoFractionDigits }}&nbsp;w%</span>
                    <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                    <span>{{ metrics.prelithiation.lithiumDensity | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                </ng-container>

                <ng-container *ngTemplateOutlet="dividerSmall" />

                <ng-container>
                    <span i18n="@@materials.activeMaterialWeight.input.label"> Aktivmaterial </span>
                    <span
                        >{{ metrics.prelithiation.activeMaterialWeightPercent | numberTwoFractionDigits }}&nbsp;w%</span
                    >
                    <ng-container *ngTemplateOutlet="dividerSmall" />
                    <span i18n="@@materials.binderWeight.input.label"> Binder </span>
                    <span>{{ binderMaterialsWeightPercent | numberTwoFractionDigits }}&nbsp;w%</span>
                    <ng-container *ngTemplateOutlet="dividerSmall" />
                </ng-container>

                <ng-container>
                    <span class="break-word">{{ lookupMaterial(design.anodeMaterials[0].materialId)?.name }}</span>
                    <span> {{ metrics.prelithiation.material1WeightPercent | numberTwoFractionDigits }}&nbsp;w%</span>
                    <ng-container *ngTemplateOutlet="dividerSmall" />
                    <span i18n="@@materials.conductiveAdditive.input.label"> Conductive Additive Materialien </span>
                    <span>{{ conductiveMaterialsWeightPercent | numberTwoFractionDigits }}&nbsp;w%</span>
                    <ng-container *ngTemplateOutlet="dividerSmall" />
                </ng-container>

                <ng-container>
                    <ng-container
                        *ngIf="
                            design.anodeMaterials.length > 1 && metrics.prelithiation.material2WeightPercent != null;
                            else emptyCells
                        "
                    >
                        <span class="break-word">
                            {{ lookupMaterial(design.anodeMaterials[1].materialId)?.name }}
                        </span>
                        <span>
                            {{ metrics.prelithiation.material2WeightPercent | numberTwoFractionDigits }}&nbsp;w%</span
                        >
                        <ng-container *ngTemplateOutlet="dividerSmall" />
                    </ng-container>
                    <ng-template #emptyCells><span></span><span></span></ng-template>
                    <span i18n="@@common.anodeDensity">Dichte Anode</span>
                    <span>{{ metrics.anode.totalDensity | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                    <ng-container *ngTemplateOutlet="dividerSmall" />
                </ng-container>

                <ng-container>
                    <span class="col-span-2" i18n="@@materials.anode.fullCellUtilization">
                        Ausnutzung Anode Vollzelle
                    </span>
                    <span class="col-span-2">
                        {{ metrics.anode.fullCellQrev | numberTwoFractionDigits }}&nbsp;mAh/G(AAM)
                    </span>
                    <ng-container *ngTemplateOutlet="dividerSmall" />
                </ng-container>

                <ng-container>
                    <span class="col-span-2" i18n="@@materials.anode.halfCellUtilization">
                        Ausnutzung Anode Halbzelle
                    </span>
                    <span class="col-span-2">
                        {{ metrics.anode.halfCellQrev | numberTwoFractionDigits }}&nbsp;mAh/G(AAM)
                    </span>
                </ng-container>
            </div>
        </p-panel>
    </div>

    <div class="col-12 lg:col-6">
        <p-panel>
            <ng-template pTemplate="header">
                <strong i18n="@@common.cathode" class="text-xl">Kathode</strong>
                <div *ngIf="cathodeWeightFormGroup.invalid" class="com-text-error">
                    <span class="error-icon"><i class="pi pi-times-circle"></i></span>
                    <span i18n="@@materials.percentages.error">Die Summe der Prozentsätze muss 100 ergeben</span>
                </div>
            </ng-template>

            <div class="css-grid">
                <ng-container
                    [ngModelGroup]="formGroupNames.cathodeWeight"
                    #cathodeWeightFormGroup="ngModelGroup"
                    comValidateGroupSumDirective
                    [desiredSum]="desiredSum"
                    [includedControlNames]="[
                        controlNames.cathodeActiveMaterialWeight,
                        controlNames.cathodeBinderPpaWeight,
                        controlNames.cathodeConductiveAdditiveWeight
                    ]"
                >
                    <ng-container>
                        <label
                            i18n="@@materials.activeMaterialWeight.input.label"
                            [htmlFor]="controlNames.cathodeActiveMaterialWeight"
                        >
                            Aktivmaterial
                        </label>
                        <div class="flex-group">
                            <p-inputNumber
                                [ngClass]="{
                                    'ng-invalid ng-dirty': cathodeWeightFormGroup.invalid
                                }"
                                #cathodeActiveMaterial
                                comCustomizeInput
                                [inputId]="controlNames.cathodeActiveMaterialWeight"
                                [name]="controlNames.cathodeActiveMaterialWeight"
                                [(ngModel)]="design.activeMaterialCathodeWeightPercent"
                                [minFractionDigits]="minFractionDigits"
                                [maxFractionDigits]="maxFractionDigits"
                                mode="decimal"
                                suffix=" w%"
                                [min]="0.01"
                                [max]="desiredSum"
                                [required]="true"
                            />
                            <ng-container
                                *ngTemplateOutlet="
                                    addRemoveButtons;
                                    context: {
                                        formGroup: cathodeWeightFormGroup,
                                        onAddButtonClick: applyAdditionalCathodeValues,
                                        onRemoveButtonClick: removeAdditionalCathodeValues
                                    }
                                "
                            />
                        </div>
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span>{{ metrics.cathode.activeMaterialDensity | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                        <ng-container *ngTemplateOutlet="dividerSmall" />
                    </ng-container>

                    <ng-container>
                        <span class="break-word">
                            {{ lookupMaterial(design.cathodeMaterials[0].materialId)?.name }}
                        </span>
                        <span class="ml-1"
                            >{{ metrics.cathode.material1WeightPercent | numberTwoFractionDigits }}&nbsp;w%</span
                        >
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span>{{ metrics.cathode.material1Density | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                        <ng-container *ngTemplateOutlet="dividerSmall" />
                    </ng-container>

                    <ng-container
                        *ngIf="
                            design.cathodeMaterials.length > 1 &&
                            metrics.cathode.material2WeightPercent != null &&
                            metrics.cathode.material2Density != null
                        "
                    >
                        <span class="break-word">
                            {{ lookupMaterial(design.cathodeMaterials[1].materialId)?.name }}
                        </span>
                        <span class="ml-1"
                            >{{ metrics.cathode.material2WeightPercent | numberTwoFractionDigits }}&nbsp;w%</span
                        >
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span>{{ metrics.cathode.material2Density | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                        <ng-container *ngTemplateOutlet="dividerSmall" />
                    </ng-container>
                    <hr class="hr" />

                    <label
                        class="col-span-4"
                        i18n="@@materials.binderWeight.input.label"
                        [htmlFor]="controlNames.cathodeBinderVersion + '0'"
                    >
                        Binder
                    </label>
                    <ng-container *ngFor="let selectedOption of design.cathodeBinderMaterials; let i = index">
                        <div class="flex-group mobile-tiny-col-span-2">
                            <p-button
                                icon="pi pi-minus"
                                (onClick)="
                                    removeVersion(
                                        selectedOption.materialId,
                                        cathodeBinderMaterialsAllOptions,
                                        design.cathodeBinderMaterials
                                    )
                                "
                                [disabled]="design.cathodeBinderMaterials.length === 1"
                            ></p-button>
                            <p-dropdown
                                [name]="controlNames.cathodeBinderVersion + i"
                                [inputId]="controlNames.cathodeBinderVersion + i"
                                [options]="cathodeBinderMaterialsAllOptions"
                                [(ngModel)]="selectedOption.materialId"
                                optionLabel="name"
                                optionValue="id"
                                (onChange)="
                                    changeBinderVersion(cathodeBinderMaterialsAllOptions, design.cathodeBinderMaterials)
                                "
                                class="col-12 xl:col-9 p-fluid"
                            ></p-dropdown>
                        </div>
                        <div class="flex-group mobile-tiny-col-span-2">
                            <p-inputNumber
                                [ngClass]="{
                                    'ng-invalid ng-dirty': cathodeWeightFormGroup.invalid
                                }"
                                comCustomizeInput
                                [inputId]="controlNames.cathodeBinderPpaWeight + i"
                                [name]="controlNames.cathodeBinderPpaWeight + i"
                                [(ngModel)]="selectedOption.weightPercent"
                                [minFractionDigits]="minFractionDigits"
                                [maxFractionDigits]="maxFractionDigits"
                                mode="decimal"
                                suffix=" w%"
                                [min]="0"
                                [required]="true"
                            />
                            <ng-container
                                *ngTemplateOutlet="
                                    addRemoveButtons;
                                    context: {
                                        formGroup: cathodeWeightFormGroup,
                                        onAddButtonClick: applyAdditionalCathodeValues,
                                        onRemoveButtonClick: removeAdditionalCathodeValues,
                                        selectedOption
                                    }
                                "
                            />
                        </div>
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span
                            >{{
                                cathodeBinderDensities.get(selectedOption.materialId) | numberTwoFractionDigits
                            }}&nbsp;g/cm³</span
                        >
                    </ng-container>
                    <ng-container>
                        <p-button
                            icon="pi pi-plus"
                            (onClick)="addVersion(cathodeBinderMaterialsAllOptions, design.cathodeBinderMaterials)"
                            *ngIf="cathodeBinderMaterialsAllOptions.length > design.cathodeBinderMaterials.length"
                        ></p-button>
                    </ng-container>
                    <hr class="hr" />

                    <label
                        class="col-span-4"
                        i18n="@@materials.conductiveAdditive.input.label"
                        [htmlFor]="controlNames.cathodeConductiveAdditiveVersion + '0'"
                    >
                        Conductive Additive Materialien
                    </label>
                    <ng-container
                        *ngFor="let selectedOption of design.cathodeConductiveAdditiveMaterials; let i = index"
                    >
                        <div class="flex-group mobile-tiny-col-span-2">
                            <p-button
                                icon="pi pi-minus"
                                (onClick)="
                                    removeVersion(
                                        selectedOption.materialId,
                                        cathodeConductiveMaterialsAllOptions,
                                        design.cathodeConductiveAdditiveMaterials
                                    )
                                "
                                [disabled]="design.cathodeConductiveAdditiveMaterials.length === 1"
                            ></p-button>
                            <p-dropdown
                                [name]="controlNames.cathodeConductiveAdditiveVersion + i"
                                [inputId]="controlNames.cathodeConductiveAdditiveVersion + i"
                                [options]="cathodeConductiveMaterialsAllOptions"
                                [(ngModel)]="selectedOption.materialId"
                                optionLabel="name"
                                optionValue="id"
                                (onChange)="
                                    changeBinderVersion(
                                        cathodeConductiveMaterialsAllOptions,
                                        design.cathodeConductiveAdditiveMaterials
                                    )
                                "
                                class="col-12 xl:col-9 p-fluid"
                            ></p-dropdown>
                        </div>
                        <div class="flex-group mobile-tiny-col-span-2">
                            <p-inputNumber
                                [ngClass]="{
                                    'ng-invalid ng-dirty': cathodeWeightFormGroup.invalid
                                }"
                                comCustomizeInput
                                [inputId]="controlNames.cathodeActiveMaterialWeight + i"
                                [name]="controlNames.cathodeActiveMaterialWeight + i"
                                [(ngModel)]="selectedOption.weightPercent"
                                [minFractionDigits]="minFractionDigits"
                                [maxFractionDigits]="maxFractionDigits"
                                mode="decimal"
                                suffix=" w%"
                                [min]="0"
                                [required]="true"
                            />
                            <ng-container
                                *ngTemplateOutlet="
                                    addRemoveButtons;
                                    context: {
                                        formGroup: cathodeWeightFormGroup,
                                        onAddButtonClick: applyAdditionalCathodeValues,
                                        onRemoveButtonClick: removeAdditionalCathodeValues,
                                        selectedOption
                                    }
                                "
                            />
                        </div>
                        <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                        <span
                            >{{
                                cathodeConductiveDensities.get(selectedOption.materialId) | numberTwoFractionDigits
                            }}&nbsp;g/cm³</span
                        >
                    </ng-container>
                    <ng-container>
                        <p-button
                            icon="pi pi-plus"
                            (onClick)="
                                addVersion(
                                    cathodeConductiveMaterialsAllOptions,
                                    design.cathodeConductiveAdditiveMaterials
                                )
                            "
                            *ngIf="
                                cathodeConductiveMaterialsAllOptions.length >
                                design.cathodeConductiveAdditiveMaterials.length
                            "
                        ></p-button>
                    </ng-container>
                </ng-container>
                <hr class="hr" />

                <ng-container>
                    <label
                        class="col-span-4"
                        i18n="@@materials.currentCollector.input.label"
                        [htmlFor]="controlNames.copperCurrentCollector"
                    >
                        Stromableiter
                    </label>
                    <div class="flex-group mobile-tiny-col-span-2">
                        <p-dropdown
                            [name]="controlNames.aluminiumCurrentCollector"
                            [inputId]="controlNames.aluminiumCurrentCollector"
                            [options]="aluminiumCurrentCollectorMaterialsAllOptions"
                            [(ngModel)]="design.aluminiumCurrentCollectorMaterialId"
                            optionLabel="name"
                            optionValue="id"
                            class="col-12 xl:col-9 p-fluid"
                        ></p-dropdown>
                    </div>
                    <span></span>
                    <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                    <span>{{ metrics.aluminiumDensity | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                </ng-container>
                <hr class="hr" />

                <ng-container>
                    <span></span>
                    <span></span>
                    <span i18n="@@common.cathodeDensity">Dichte Kathode</span>
                    <span>{{ metrics.cathode.totalDensity | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                    <ng-container *ngTemplateOutlet="dividerSmall" />
                </ng-container>

                <ng-container>
                    <span class="col-span-2" i18n="@@materials.cathode.fullCellUtilization">
                        Ausnutzung Kathode Vollzelle
                    </span>
                    <span class="col-span-2">
                        {{ metrics.cathode.fullCellQrev | numberTwoFractionDigits }}&nbsp;mAh/G(CAM)
                    </span>
                    <ng-container *ngTemplateOutlet="dividerSmall" />
                </ng-container>

                <ng-container>
                    <span class="col-span-2" i18n="@@materials.cathode.halfCellUtilization">
                        Ausnutzung Kathode Halbzelle
                    </span>
                    <span class="col-span-2">
                        {{ metrics.cathode.halfCellQrev | numberTwoFractionDigits }}&nbsp;mAh/G(CAM)
                    </span>
                    <ng-container *ngTemplateOutlet="dividerSmall" />
                </ng-container>
            </div>
        </p-panel>

        <p-panel class="mt-3 block">
            <ng-template pTemplate="header">
                <strong i18n="@@common.electrolyteSeparator" class="text-xl"> Electrolyt + Separator </strong>
            </ng-template>

            <div class="css-grid">
                <label
                    class="col-span-4"
                    i18n="@@materials.electrolyte.input.label"
                    [htmlFor]="controlNames.electrolyte"
                >
                    Elektrolyt
                </label>
                <ng-container>
                    <div class="flex-group mobile-tiny-col-span-2">
                        <p-dropdown
                            [name]="controlNames.electrolyte"
                            [inputId]="controlNames.electrolyte"
                            [options]="electrolyteMaterialsAllOptions"
                            [(ngModel)]="design.electrolyteMaterialId"
                            optionLabel="name"
                            optionValue="id"
                            class="col-12 xl:col-9 p-fluid"
                        ></p-dropdown>
                    </div>
                    <span></span>
                    <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                    <span>{{ metrics.electrolyteDensity | numberTwoFractionDigits }}&nbsp;g/cm³</span>
                </ng-container>

                <hr class="hr" />

                <label class="col-span-4" i18n="@@materials.separator.input.label" [htmlFor]="controlNames.separator">
                    Separator
                </label>
                <ng-container>
                    <div class="flex-group mobile-tiny-col-span-2">
                        <p-dropdown
                            [name]="controlNames.separator"
                            [inputId]="controlNames.separator"
                            [options]="separatorMaterialsAllOptions"
                            [(ngModel)]="design.separatorMaterialId"
                            optionLabel="name"
                            optionValue="id"
                            class="col-12 xl:col-9 p-fluid"
                        ></p-dropdown>
                    </div>
                    <span></span>
                    <ng-container *ngTemplateOutlet="density; context: { isMobileRight: true }" />
                    <span> {{ metrics.separatorDensity | numberTwoFractionDigits }}&nbsp;g/cm³ </span>
                </ng-container>
                <ng-container>
                    <span></span>
                    <span></span>
                    <span i18n="@@common.porosity" [ngClass]="{ 'density-mobile': true }">Porosität</span>
                    <span> {{ metrics.separatorPorousness | numberTwoFractionDigits }}&nbsp;% </span>
                </ng-container>
            </div>
        </p-panel>
    </div>
</div>

<ng-template
    #addRemoveButtons
    let-formGroup="formGroup"
    let-onAddButtonClick="onAddButtonClick"
    let-onRemoveButtonClick="onRemoveButtonClick"
    let-selectedOption="selectedOption"
>
    <button
        type="button"
        class="p-link apply-remove-button"
        *ngIf="
            formGroup.errors &&
            formGroup.errors[sumValidationErrorName] &&
            formGroup.errors[sumValidationErrorName] < desiredSum
        "
        (click)="onAddButtonClick(selectedOption)"
    >
        <i class="pi pi-plus-circle"></i>
    </button>

    <button
        type="button"
        class="p-link apply-remove-button"
        *ngIf="
            formGroup.errors &&
            formGroup.errors[sumValidationErrorName] &&
            formGroup.errors[sumValidationErrorName] > desiredSum
        "
        (click)="onRemoveButtonClick(selectedOption)"
    >
        <i class="pi pi-minus-circle"></i>
    </button>
</ng-template>
