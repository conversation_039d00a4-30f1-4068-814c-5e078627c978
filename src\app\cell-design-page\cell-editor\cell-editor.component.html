<div *ngIf="loading" class="flex justify-content-center align-items-center progress-spinner">
    <p-progressSpinner />
</div>

<div
    *ngIf="
        (isPouch(design) && isPouchMetrics(design, metrics)) || (isPrisma(design) && isPrismaMetrics(design, metrics))
    "
    class="grid align-items-stretch"
>
    <div class="col-12 lg:col-6">
        <p-panel i18n-header="@@cell.cellGeometry" header="Zellgeometrie">
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.cellFormat"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.cellFormat"
                    >Zellformat</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-dropdown
                        [name]="controlNames.cellFormat"
                        [inputId]="controlNames.cellFormat"
                        [options]="cellFormatOptions"
                        [(ngModel)]="design.cellFormatId"
                        (onChange)="changeCellFormat($event)"
                        optionLabel="name"
                        optionValue="id"
                    ></p-dropdown>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.editFormat"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.editFormat"
                    >Format bearbeiten?</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputSwitch
                        [inputId]="controlNames.editFormat"
                        [name]="controlNames.editFormat"
                        [(ngModel)]="design.cellFormatIsDefault"
                        [trueValue]="false"
                        [falseValue]="true"
                        (onChange)="setIsDefault($event)"
                    ></p-inputSwitch>
                </div>
            </div>
            <div *ngIf="isPrisma(design)" class="field grid">
                <label
                    [htmlFor]="controlNames.hasTwoSubstacks"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.hasTwoSubstacks"
                    >Zusammenbau aus 2 Substacks?</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputSwitch
                        [inputId]="controlNames.hasTwoSubstacks"
                        [name]="controlNames.hasTwoSubstacks"
                        [(ngModel)]="design.cellFormatProperties.hasTwoSubstacks"
                    ></p-inputSwitch>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.celltype.label">Zelltyp</label>
                <div *ngIf="isPouch(design)" class="col-12 xl:col-9 pl-2" i18n="@@cell.cellType.pouch">Pouch</div>
                <div *ngIf="isPrisma(design)" class="col-12 xl:col-9 pl-2" i18n="@@cell.cellType.prisma">
                    Prismatisch
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.cellLength"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.cellLength"
                    >Zell Länge</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        #cellLength
                        [name]="controlNames.cellLength"
                        [inputId]="controlNames.cellLength"
                        [(ngModel)]="design.cellFormatProperties.cellLength"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        [min]="0.0"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.cellWidth"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.cellWidth"
                    >Zell Breite</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.cellWidth"
                        [inputId]="controlNames.cellWidth"
                        [(ngModel)]="design.cellFormatProperties.cellWidth"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        [min]="0.0"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.cellThickness"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.cellThickness"
                    >Zell Dicke</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.cellThickness"
                        [inputId]="controlNames.cellThickness"
                        [(ngModel)]="design.cellFormatProperties.cellThickness"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        [min]="0.0"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0">
                    <strong i18n="@@cell.cellVolume">Zellvolumen</strong>
                </label>
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.mililitre"
                        >{{ metrics?.cellVolume | numberTwoFractionDigits }} ml</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.housingWeight"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.caseWeight"
                    >Gehäuseaewicht (inkl. Tabs)</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <com-inplace-edit
                        [value]="
                            design.cellFormatProperties.housingWeight != null
                                ? design.cellFormatProperties.housingWeight
                                : metrics?.housingWeight ?? null
                        "
                        [disabled]="design.cellFormatIsDefault"
                        [showUndo]="design.cellFormatProperties.housingWeight != null"
                        (valueChange)="design.cellFormatProperties.housingWeight = $event; notifier.refreshMetrics()"
                    >
                        <ng-template #display let-value="value">
                            <span i11n="@@common.units.grams">{{ value | numberTwoFractionDigits }} g</span>
                        </ng-template>
                        <ng-template #edit let-value="value" let-setValue="setValue">
                            <p-inputNumber
                                comCustomizeInput
                                [ngModel]="value"
                                [ngModelOptions]="{ standalone: true }"
                                (ngModelChange)="setValue($event)"
                                [maxFractionDigits]="maxFractionDigits"
                                [minFractionDigits]="0"
                                [min]="0.0"
                                [disabled]="design.cellFormatIsDefault"
                                suffix=" g"
                            ></p-inputNumber>
                        </ng-template>
                    </com-inplace-edit>
                </div>
            </div>
        </p-panel>
        <p-panel i18n-header="@@cell.coatingSurfaces" header="Beschichtungsflächen">
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingLengthCathode"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingLengthCathode"
                    >Beschichtungslänge Kathode</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.coatingLengthCathode"
                        [inputId]="controlNames.coatingLengthCathode"
                        [(ngModel)]="design.cellFormatProperties.coatingLengthCathode"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="design.cellFormatProperties.cellLength"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingWidthCathode"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingWidthCathode"
                    >Beschichtungsbreite Kathode</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.coatingWidthCathode"
                        [inputId]="controlNames.coatingWidthCathode"
                        [(ngModel)]="design.cellFormatProperties.coatingWidthCathode"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="design.cellFormatProperties.cellWidth"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.coatingAreaCathode"
                    >Beschichtungsfläche Kathode</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.squareMillimeters"
                        >{{ metrics?.cathodeCoatingArea | numberTwoFractionDigits }} mm²</span
                    >
                </div>
            </div>
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingLengthAnode"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingLengthAnode"
                    >Beschichtungslänge Anode</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.coatingLengthAnode"
                        [inputId]="controlNames.coatingLengthAnode"
                        [(ngModel)]="design.cellFormatProperties.coatingLengthAnode"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="design.cellFormatProperties.cellLength"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingWidthAnode"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingWidthAnode"
                    >Beschichtungsbreite Anode</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.coatingWidthAnode"
                        [inputId]="controlNames.coatingWidthAnode"
                        [(ngModel)]="design.cellFormatProperties.coatingWidthAnode"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="design.cellFormatProperties.cellWidth"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.coatingAreaAnode"
                    >Beschichtungsfläche Anode</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.squareMillimeters"
                        >{{ metrics?.anodeCoatingArea | numberTwoFractionDigits }} mm²</span
                    >
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingLengthSeparator"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingLengthSeparator"
                    >Beschichtungslänge Separator</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.coatingLengthSeparator"
                        [inputId]="controlNames.coatingLengthSeparator"
                        [(ngModel)]="design.cellFormatProperties.coatingLengthSeparator"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="design.cellFormatProperties.cellLength"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingWidthSeparator"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingWidthSeparator"
                    >Beschichtungsbreite Separator</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.coatingWidthSeparator"
                        [inputId]="controlNames.coatingWidthSeparator"
                        [(ngModel)]="design.cellFormatProperties.coatingWidthSeparator"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="design.cellFormatProperties.cellWidth"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.coatingAreaSeparator"
                    >Beschichtungsfläche Separator</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.squareMillimeters"
                        >{{ metrics?.separatorCoatingArea | numberTwoFractionDigits }} mm²</span
                    >
                </div>
            </div>
        </p-panel>
    </div>

    <div class="col-12 lg:col-6">
        <p-panel i18n-header="@@cell.cellLayerCalculation" header="Zelllagenberechnung">
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.housingThickness"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.housingThickness"
                    >Pouch/Gehäuse-Wandstärke</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.housingThickness"
                        [inputId]="controlNames.housingThickness"
                        [(ngModel)]="design.cellFormatProperties.housingThickness"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="
                            -1 + design.cellFormatProperties.cellThickness / 2 < 0
                                ? 0
                                : -1 + design.cellFormatProperties.cellThickness / 2
                        "
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.swellingBuffer"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.swellingBuffer"
                    >Swelling-Vorhalt</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.swellingBuffer"
                        [inputId]="controlNames.swellingBuffer"
                        [(ngModel)]="design.cellFormatProperties.swellingBuffer"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="-2"
                        [max]="design.cellFormatProperties.cellThickness - 1"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.electrolyteSwelling"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.electrolyteSwelling"
                    >Elektrolytswelling</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.electrolyteSwelling"
                        [inputId]="controlNames.electrolyteSwelling"
                        [(ngModel)]="design.electrolyteSwelling"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="-30"
                        [max]="30"
                        [required]="true"
                        suffix=" %"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.electrolyteSwelling">Elektrolytswelling</label>
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.millimeter"
                        >{{ metrics?.electrolyteSwelling | numberTwoFractionDigits }} mm</span
                    >
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.assemblyClearance">Montagefreiraum</label>
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.millimeter"
                        >{{ metrics?.assemblyClearance | numberTwoFractionDigits }} mm</span
                    >
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.maxTotalLayerThickness"
                    >Max. Gesamtdicke Zelllagen</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.millimeter"
                        >{{ metrics?.cellLayerThicknessMax | numberTwoFractionDigits }} mm</span
                    >
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.totalLayerThickness"
                    >Gesamtdicke Zelllagen</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.millimeter"
                        >{{ metrics?.cellLayerThicknessTotal | numberTwoFractionDigits }} mm</span
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.layerFreeSpace">Freiraum Zelllagen</strong>
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.micrometer"
                        >{{ metrics?.cellLayerDelta | numberTwoFractionDigits }} μm</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.layerFreeSpaceOverThickness"
                    >Freiraum Zelllagen / Dicke Zelllage</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong>{{ metrics?.ratioCellLayerDeltaThickness | numberTwoFractionDigits }}</strong>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.numberOfLayersCathode"
                    >Anzahl Lagen Kathode</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span>{{ metrics?.cathodeLayerCount | numberTwoFractionDigits }}</span>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.numberOfLayersAnode">Anzahl Lagen Anode</label>
                <div class="col-12 xl:col-9 pl-2">
                    <span>{{ metrics?.anodeLayerCount | numberTwoFractionDigits }}</span>
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.numberOfActiveLayers"
                    >Anzahl aktive Lagen</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong>{{ metrics?.activeLayerCount | numberTwoFractionDigits }}</strong>
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.chargePerLayerC10"
                    >Ladung pro Zelllage C/1O</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.amperHours"
                        >{{ metrics?.capacityPerLayerC10 | numberTwoFractionDigits }} Ah</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.energyPerLayerC10"
                    >Energie pro Zelllage C/1O</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.wattHours"
                        >{{ metrics?.energyPerLayerC10 | numberTwoFractionDigits }} Wh</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.chargePerLayerC3"
                    >Ladung pro Zelllage C/3</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.amperHours"
                        >{{ metrics?.capacityPerLayerC3 | numberTwoFractionDigits }} Ah</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.energyPerLayerC3"
                    >Energie pro Zelllage C/3</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.wattHours"
                        >{{ metrics?.energyPerLayerC3 | numberTwoFractionDigits }} Wh</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.numberOfLayersSeparator"
                    >Anzahl Lagen Separator</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span>{{ metrics?.separatorLayerCount | numberTwoFractionDigits }}</span>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.totalAreaSeparator"
                    >Gesamtfläche Separator</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span>{{ metrics?.separatorAreaTotal | numberTwoFractionDigits }}</span>
                </div>
            </div>
        </p-panel>
    </div>
</div>

<div *ngIf="isCylinder(design) && isCylinderMetrics(design, metrics)" class="grid align-items-stretch">
    <div class="col-12 lg:col-6">
        <p-panel i18n-header="@@cell.cellGeometry" header="Zellgeometrie">
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.cellFormat"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.cellFormat"
                    >Zellformat</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-dropdown
                        [name]="controlNames.cellFormat"
                        [inputId]="controlNames.cellFormat"
                        [options]="cellFormatOptions"
                        [(ngModel)]="design.cellFormatId"
                        (onChange)="changeCellFormat($event)"
                        optionLabel="name"
                        optionValue="id"
                    ></p-dropdown>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.editFormat"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.editFormat"
                    >Format bearbeiten?</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputSwitch
                        [inputId]="controlNames.editFormat"
                        [name]="controlNames.editFormat"
                        [(ngModel)]="design.cellFormatIsDefault"
                        [trueValue]="false"
                        [falseValue]="true"
                        (onChange)="setIsDefault($event)"
                    ></p-inputSwitch>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.celltype.label">Zelltyp</label>
                <div class="col-12 xl:col-9 pl-2" i18n="@@cell.cellType.cylinder">Zylindrisch</div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.cellDiameter"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.cellDiameter"
                    >Zell Durchmesser</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.cellDiameter"
                        [inputId]="controlNames.cellDiameter"
                        [(ngModel)]="design.cellFormatProperties.cellDiameter"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        [min]="0.0"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.cellHeight"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.cellHeight"
                    >Zell Höhe</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.cellHeight"
                        [inputId]="controlNames.cellHeight"
                        [(ngModel)]="design.cellFormatProperties.cellHeight"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        [min]="0.0"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.cellCoreDiameter"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.cellCoreDiameter"
                    >Zellkern Durchmesser</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.cellCoreDiameter"
                        [inputId]="controlNames.cellCoreDiameter"
                        [(ngModel)]="design.cellFormatProperties.cellCoreDiameter"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        [min]="0.0"
                        [max]="
                            metrics && metrics.cellLayerDiameterMax != null
                                ? metrics.cellLayerDiameterMax - 0.00000001
                                : $any(undefined)
                        "
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0">
                    <strong i18n="@@cell.cellVolume">Zellvolumen</strong>
                </label>
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.mililitre"
                        >{{ metrics?.cellVolume | numberTwoFractionDigits }} ml</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.housingWeight"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.caseWeight"
                    >Gehäuseaewicht (inkl. Tabs)</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <com-inplace-edit
                        [value]="
                            design.cellFormatProperties.housingWeight != null
                                ? design.cellFormatProperties.housingWeight
                                : metrics?.housingWeight ?? null
                        "
                        [disabled]="design.cellFormatIsDefault"
                        [showUndo]="design.cellFormatProperties.housingWeight != null"
                        (valueChange)="design.cellFormatProperties.housingWeight = $event; notifier.refreshMetrics()"
                    >
                        <ng-template #display let-value="value">
                            <span i11n="@@common.units.grams">{{ value | numberTwoFractionDigits }} g</span>
                        </ng-template>
                        <ng-template #edit let-value="value" let-setValue="setValue">
                            <p-inputNumber
                                comCustomizeInput
                                [ngModel]="value"
                                [ngModelOptions]="{ standalone: true }"
                                (ngModelChange)="setValue($event)"
                                [maxFractionDigits]="maxFractionDigits"
                                [minFractionDigits]="0"
                                [min]="0.0"
                                [disabled]="design.cellFormatIsDefault"
                                suffix=" g"
                            ></p-inputNumber>
                        </ng-template>
                    </com-inplace-edit>
                </div>
            </div>
        </p-panel>
        <p-panel i18n-header="@@cell.coatingSurfaces" header="Beschichtungsflächen">
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingLengthCathode"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingLengthCathode"
                    >Beschichtungslänge Kathode</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <com-inplace-edit
                        [value]="
                            design.cellFormatProperties.coatingLengthCathode != null
                                ? design.cellFormatProperties.coatingLengthCathode
                                : metrics?.cathodeCoatingLength ?? null
                        "
                        [disabled]="design.cellFormatIsDefault"
                        [showUndo]="design.cellFormatProperties.coatingLengthCathode != null"
                        (valueChange)="
                            design.cellFormatProperties.coatingLengthCathode = $event; notifier.refreshMetrics()
                        "
                    >
                        <ng-template #display let-value="value">
                            <span i11n="@@common.units.millimeter">{{ value | numberTwoFractionDigits }} mm</span>
                        </ng-template>
                        <ng-template #edit let-value="value" let-setValue="setValue">
                            <p-inputNumber
                                comCustomizeInput
                                [ngModel]="value"
                                [ngModelOptions]="{ standalone: true }"
                                (ngModelChange)="setValue($event)"
                                [maxFractionDigits]="maxFractionDigits"
                                [minFractionDigits]="0"
                                [min]="0.0"
                                [max]="
                                    metrics && metrics.cathodeLengthTotal != null
                                        ? metrics.cathodeLengthTotal
                                        : $any(undefined)
                                "
                                [disabled]="design.cellFormatIsDefault"
                                suffix=" mm"
                            ></p-inputNumber>
                        </ng-template>
                    </com-inplace-edit>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingWidthCathode"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingWidthCathode"
                    >Beschichtungsbreite Kathode</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.coatingWidthCathode"
                        [inputId]="controlNames.coatingWidthCathode"
                        [(ngModel)]="design.cellFormatProperties.coatingWidthCathode"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="design.cellFormatProperties.cellHeight"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.coatingAreaCathode"
                    >Beschichtungsfläche Kathode</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.squareMillimeters"
                        >{{ metrics?.cathodeCoatingArea | numberTwoFractionDigits }} mm²</span
                    >
                </div>
            </div>
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingLengthAnode"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingLengthAnode"
                    >Beschichtungslänge Anode</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <com-inplace-edit
                        [value]="
                            design.cellFormatProperties.coatingLengthAnode != null
                                ? design.cellFormatProperties.coatingLengthAnode
                                : metrics?.anodeCoatingLength ?? null
                        "
                        [disabled]="design.cellFormatIsDefault"
                        [showUndo]="design.cellFormatProperties.coatingLengthAnode != null"
                        (valueChange)="
                            design.cellFormatProperties.coatingLengthAnode = $event; notifier.refreshMetrics()
                        "
                    >
                        <ng-template #display let-value="value">
                            <span i11n="@@common.units.millimeter">{{ value | numberTwoFractionDigits }} mm</span>
                        </ng-template>
                        <ng-template #edit let-value="value" let-setValue="setValue">
                            <p-inputNumber
                                comCustomizeInput
                                [ngModel]="value"
                                [ngModelOptions]="{ standalone: true }"
                                (ngModelChange)="setValue($event)"
                                [maxFractionDigits]="maxFractionDigits"
                                [minFractionDigits]="0"
                                [min]="0.0"
                                [max]="
                                    metrics && metrics.anodeLengthTotal != null
                                        ? metrics.anodeLengthTotal
                                        : $any(undefined)
                                "
                                [disabled]="design.cellFormatIsDefault"
                                suffix=" mm"
                            ></p-inputNumber>
                        </ng-template>
                    </com-inplace-edit>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingWidthAnode"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingWidthAnode"
                    >Beschichtungsbreite Anode</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.coatingWidthAnode"
                        [inputId]="controlNames.coatingWidthAnode"
                        [(ngModel)]="design.cellFormatProperties.coatingWidthAnode"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="design.cellFormatProperties.cellHeight"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.coatingAreaAnode"
                    >Beschichtungsfläche Anode</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.squareMillimeters"
                        >{{ metrics?.anodeCoatingArea | numberTwoFractionDigits }} mm²</span
                    >
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingLengthSeparator"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingLengthSeparator"
                    >Beschichtungslänge Separator</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <com-inplace-edit
                        [value]="
                            design.cellFormatProperties.coatingLengthSeparator != null
                                ? design.cellFormatProperties.coatingLengthSeparator
                                : metrics?.separatorCoatingLength ?? null
                        "
                        [disabled]="design.cellFormatIsDefault"
                        [showUndo]="design.cellFormatProperties.coatingLengthSeparator != null"
                        (valueChange)="
                            design.cellFormatProperties.coatingLengthSeparator = $event; notifier.refreshMetrics()
                        "
                    >
                        <ng-template #display let-value="value">
                            <span i11n="@@common.units.millimeter">{{ value | numberTwoFractionDigits }} mm</span>
                        </ng-template>
                        <ng-template #edit let-value="value" let-setValue="setValue">
                            <!-- TODO: What's the max for separator coating length?  -->
                            <p-inputNumber
                                comCustomizeInput
                                [ngModel]="value"
                                [ngModelOptions]="{ standalone: true }"
                                (ngModelChange)="setValue($event)"
                                [maxFractionDigits]="maxFractionDigits"
                                [minFractionDigits]="0"
                                [min]="0.0"
                                [disabled]="design.cellFormatIsDefault"
                                suffix=" mm"
                            ></p-inputNumber>
                        </ng-template>
                    </com-inplace-edit>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.coatingWidthSeparator"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.coatingWidthSeparator"
                    >Beschichtungsbreite Separator</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.coatingWidthSeparator"
                        [inputId]="controlNames.coatingWidthSeparator"
                        [(ngModel)]="design.cellFormatProperties.coatingWidthSeparator"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="design.cellFormatProperties.cellHeight"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.coatingAreaSeparator"
                    >Beschichtungsfläche Separator</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.squareMillimeters"
                        >{{ metrics?.separatorCoatingArea | numberTwoFractionDigits }} mm²</span
                    >
                </div>
            </div>
        </p-panel>
    </div>

    <div class="col-12 lg:col-6">
        <p-panel i18n-header="@@cell.cellLayerCalculation" header="Zelllagenberechnung">
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.housingThickness"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.housingThickness"
                    >Pouch/Gehäuse-Wandstärke</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.housingThickness"
                        [inputId]="controlNames.housingThickness"
                        [(ngModel)]="design.cellFormatProperties.housingThickness"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0.0"
                        [max]="(metrics?.cellLayerDiameterMax ?? 100000) / 2"
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.swellingBuffer"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.swellingBuffer"
                    >Swelling-Vorhalt</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.swellingBuffer"
                        [inputId]="controlNames.swellingBuffer"
                        [(ngModel)]="design.cellFormatProperties.swellingBuffer"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0"
                        [max]="
                            design.cellFormatProperties.cellDiameter - 1 < 0
                                ? 0
                                : design.cellFormatProperties.cellDiameter - 1
                        "
                        [disabled]="design.cellFormatIsDefault"
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>
            <div class="field grid">
                <label
                    [htmlFor]="controlNames.electrolyteSwelling"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.electrolyteSwelling"
                    >Elektrolytswelling</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.electrolyteSwelling"
                        [inputId]="controlNames.electrolyteSwelling"
                        [(ngModel)]="design.electrolyteSwelling"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        comConstrainValue
                        [min]="0"
                        [max]="30"
                        [required]="true"
                        suffix=" %"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.electrolyteSwelling">Elektrolytswelling</label>
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.millimeter"
                        >{{ metrics?.electrolyteSwelling | numberTwoFractionDigits }} mm</span
                    >
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.assemblyClearance">Montagefreiraum</label>
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.millimeter"
                        >{{ metrics?.assemblyClearance | numberTwoFractionDigits }} mm</span
                    >
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.cellLayerDiameterMax"
                    >Max. Gesamtdurchmesser Zelllagen</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.millimeter"
                        >{{ metrics?.cellLayerDiameterMax | numberTwoFractionDigits }} mm</span
                    >
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.cellLayerDiameterTotal"
                    >Gesamtdurchmesser Zelllagen</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span i18n="@@common.units.millimeter"
                        >{{ metrics?.cellLayerDiameterTotal | numberTwoFractionDigits }} mm</span
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.cathodeLengthTotal"
                    >Gesamtlänge Kathode</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.millimeter"
                        >{{ metrics?.cathodeLengthTotal | numberTwoFractionDigits }} mm</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.anodeLengthTotal">Gesamtlänge Anode</strong>
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.millimeter"
                        >{{ metrics?.anodeLengthTotal | numberTwoFractionDigits }} mm</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <label
                    [htmlFor]="controlNames.anodeOverhang"
                    class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@cell.anodeOverhang"
                    >Anodenüberstand</label
                >
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.anodeOverhang"
                        [inputId]="controlNames.anodeOverhang"
                        [(ngModel)]="design.cellFormatProperties.anodeOverhang"
                        [maxFractionDigits]="maxFractionDigits"
                        [minFractionDigits]="0"
                        [min]="0.0"
                        comConstrainValue
                        [required]="true"
                        suffix=" mm"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.cathodeWindingCount"
                    >Anzahl Windungen Kathode</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span>{{ metrics?.cathodeWindingCount | numberTwoFractionDigits }}</span>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.anodeWindingCount"
                    >Anzahl Windungen Anode</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span>{{ metrics?.anodeWindingCount | numberTwoFractionDigits }}</span>
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.numberOfActiveLayers"
                    >Anzahl aktive Lagen</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong>{{ metrics?.activeLayerCount | numberTwoFractionDigits }}</strong>
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.chargePerLayerC10"
                    >Ladung pro Zelllage C/1O</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.amperHours"
                        >{{ metrics?.capacityPerLayerC10 | numberTwoFractionDigits }} Ah</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.energyPerLayerC10"
                    >Energie pro Zelllage C/1O</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.wattHours"
                        >{{ metrics?.energyPerLayerC10 | numberTwoFractionDigits }} Wh</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.chargePerLayerC3"
                    >Ladung pro Zelllage C/3</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.amperHours"
                        >{{ metrics?.capacityPerLayerC3 | numberTwoFractionDigits }} Ah</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <strong class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.energyPerLayerC3"
                    >Energie pro Zelllage C/3</strong
                >
                <div class="col-12 xl:col-9 pl-2">
                    <strong i18n="@@common.units.wattHours"
                        >{{ metrics?.energyPerLayerC3 | numberTwoFractionDigits }} Wh</strong
                    >
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.separatorWindingCount"
                    >Anzahl Windungen Separator</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span>{{ metrics?.separatorWindingCount | numberTwoFractionDigits }}</span>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@cell.totalAreaSeparator"
                    >Gesamtfläche Separator</label
                >
                <div class="col-12 xl:col-9 pl-2">
                    <span>{{ metrics?.separatorAreaTotal | numberTwoFractionDigits }}</span>
                </div>
            </div>
        </p-panel>
    </div>
</div>
