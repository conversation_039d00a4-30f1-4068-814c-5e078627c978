import { deepEquals } from './deep-equals';

describe('deepEquals', () => {
    it('should compare equal booleans correctly', () => {
        const left = true;
        const right = true;

        expect(deepEquals(left, right)).toBeTrue();
    });

    it('should compare unequal booleans correctly', () => {
        const left = false;
        const right = true;

        expect(deepEquals(left, right)).toBeFalse();
    });

    it('should compare equal numbers correctly', () => {
        const left = 5;
        const right = 5;

        expect(deepEquals(left, right)).toBeTrue();
    });

    it('should compare unequal numbers correctly', () => {
        const left = 3;
        const right = 5;

        expect(deepEquals(left, right)).toBeFalse();
    });

    it('should compare equal strings correctly', () => {
        const left = 'Some string';
        const right = 'Some string';

        expect(deepEquals(left, right)).toBeTrue();
    });

    it('should compare unequal strings correctly', () => {
        const left = 'Some string';
        const right = 'Some other string';

        expect(deepEquals(left, right)).toBeFalse();
    });

    it('should compare equal objects correctly', () => {
        const left = {
            a: 10,
            b: false,
            key: 'Key',
        };

        const right = {
            a: 10,
            b: false,
            key: 'Key',
        };

        expect(deepEquals(left, right)).toBeTrue();
    });

    it('should compare unequal objects correctly', () => {
        const left = {
            a: 10,
            b: false,
            key: 'Key',
        };

        const right = {
            a: 10,
            b: true,
            key: 'Key',
        };

        expect(deepEquals(left, right)).toBeFalse();
    });

    it('should compare equal arrays correctly', () => {
        const left = [1, 5, false, 'Test'];

        const right = [1, 5, false, 'Test'];

        expect(deepEquals(left, right)).toBeTrue();
    });

    it('should compare unequal arrays correctly', () => {
        const left = [1, 5, false, 'Test'];

        const right = [1, 8, false, 'Test'];

        expect(deepEquals(left, right)).toBeFalse();
    });

    it('should compare equal nested objects correctly', () => {
        const left = [
            {
                arr: [[1], { val1: 1, val2: 2 }, 3],
            },
            {
                nested: {
                    val: true,
                },
            },
            false,
            'Test',
        ];

        const right = [
            {
                arr: [[1], { val1: 1, val2: 2 }, 3],
            },
            {
                nested: {
                    val: true,
                },
            },
            false,
            'Test',
        ];

        expect(deepEquals(left, right)).toBeTrue();
    });

    it('should compare unequal nested objects correctly (1)', () => {
        const left = [
            {
                arr: [[1], { val1: 1, val2: 2 }, 3],
            },
            {
                nested: {
                    val: true,
                },
            },
            false,
            'Test',
        ];

        const right = [
            {
                arr: [[2], { val1: 1, val2: 2 }, 3],
            },
            {
                nested: {
                    val: true,
                },
            },
            false,
            'Test',
        ];

        expect(deepEquals(left, right)).toBeFalse();
    });

    it('should compare unequal nested objects correctly (2)', () => {
        const left = [
            {
                arr: [[1], { val1: 1, val2: 2 }, 3],
            },
            {
                nested: {
                    val: false,
                },
            },
            false,
            'Test',
        ];

        const right = [
            {
                arr: [[1], { val1: 1, val2: 2 }, 3],
            },
            {
                nested: {
                    val: true,
                },
            },
            false,
            'Test',
        ];

        expect(deepEquals(left, right)).toBeFalse();
    });

    it('should compare unequal nested objects correctly (3)', () => {
        const left = [
            {
                arr: [[1], { val1: 1, val2: 42 }, 3],
            },
            {
                nested: {
                    val: true,
                },
            },
            false,
            'Test',
        ];

        const right = [
            {
                arr: [[1], { val1: 1, val2: 2 }, 3],
            },
            {
                nested: {
                    val: true,
                },
            },
            false,
            'Test',
        ];

        expect(deepEquals(left, right)).toBeFalse();
    });
});
