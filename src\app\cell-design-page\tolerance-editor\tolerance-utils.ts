import { ChartData } from 'chart.js';
import { ToleranceResult } from '@com/services/tolerance.service';

export interface HistogramBin {
    min: number;
    max: number;
    count: number;
}

export function calculateUniqueValuesCount(values: number[]): number {
    return new Set(values.map((v) => v.toFixed(6))).size; // Using toFixed(6) to handle floating point precision
}

export function createHistogramBins(values: number[], binCount: number, uniqueValuesCount: number): HistogramBin[] {
    if (!values.length) return [];

    if (uniqueValuesCount === 1) {
        const value = values[0];

        return [{ min: value, max: value, count: values.length }];
    }

    const min = Math.min(...values);
    const max = Math.max(...values);
    const range = max - min;
    const binSize = range / binCount;

    const bins: HistogramBin[] = [];
    for (let i = 0; i < binCount; i++) {
        const binMin = min + i * binSize;
        const binMax = binMin + binSize;
        const count = values.filter((v) => v >= binMin && v < binMax).length;
        bins.push({ min: binMin, max: binMax, count });
    }

    const lastBin = bins[bins.length - 1];
    if (lastBin) {
        lastBin.count += values.filter((v) => v === max).length;
    }

    return bins;
}

export function createBinLabels(values: number[], binCount: number, uniqueValuesCount: number): string[] {
    if (!values.length) return [];

    // If all values are the same, return a single label
    if (uniqueValuesCount === 1) {
        return [`${values[0].toFixed(2)}`];
    }

    const min = Math.min(...values);
    const max = Math.max(...values);
    const range = max - min;
    const binSize = range / binCount;

    return Array.from({ length: binCount }, (_, i) => {
        const binMin = min + i * binSize;
        const binMax = binMin + binSize;

        return `${binMin.toFixed(2)}-${binMax.toFixed(2)}`;
    });
}

export function createHistogramData(
    result: ToleranceResult,
    validValues: number[],
    binCount: number,
    uniqueValuesCount: number
): ChartData | null {
    if (validValues.length === 0) {
        return null;
    }

    // Special case: if all values are the same, show a single bin
    if (uniqueValuesCount === 1) {
        const value = validValues[0];

        return {
            labels: [`${value.toFixed(2)}`],
            datasets: [
                {
                    label: result.name,
                    data: [validValues.length],
                    borderWidth: 1,
                },
            ],
        };
    }

    const bins = createHistogramBins(validValues, binCount, uniqueValuesCount);
    const labels = createBinLabels(validValues, binCount, uniqueValuesCount);

    return {
        labels,
        datasets: [
            {
                label: result.name,
                data: bins.map((bin) => bin.count),
                borderWidth: 1,
            },
        ],
    };
}
