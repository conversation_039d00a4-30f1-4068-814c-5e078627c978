import { Component, Input } from '@angular/core';
import { CellDesign, CellDesignCylinder, CellDesignPouch, CellDesignPrisma } from '@com/services/cell-design.service';
import {
    CellEditorCylinderMetrics,
    CellEditorMetrics,
    CellEditorPouchMetrics,
    CellEditorPrismaMetrics,
} from '@com/services/cell-design-metrics.service';
import { CellFormats, CellGeometryEnum } from '@com/services/cell-format.service';
import { IdLookup } from '@com/app/cell-design-page/types';
import { deepCloneDto } from '@com/utils/object';
import { ControlContainer, NgModelGroup } from '@angular/forms';
import { NotifierService } from '@com/services/notifier.service';

const CONTROL_NAMES = {
    cellFormat: 'cellFormat',
    editFormat: 'editFormat',
    cellLength: 'cellLength',
    cellThickness: 'cellThickness',
    cellWidth: 'cellWidth',
    housingWeight: 'housingWeight',
    hasTwoSubstacks: 'hasTwoSubstacks',
    coatingLengthCathode: 'coatingLengthCathode',
    coatingWidthCathode: 'coatingWidthCathode',
    coatingLengthAnode: 'coatingLengthAnode',
    coatingWidthAnode: 'coatingWidthAnode',
    coatingLengthSeparator: 'coatingLengthSeparator',
    coatingWidthSeparator: 'coatingWidthSeparator',
    housingThickness: 'housingThickness',
    swellingBuffer: 'swellingBuffer',
    electrolyteSwelling: 'electrolyteSwelling',
    cellDiameter: 'cellDiameter',
    cellHeight: 'cellHeight',
    cellCoreDiameter: 'cellCoreDiameter',
    anodeOverhang: 'anodeOverhang',
} as const;

@Component({
    selector: 'com-cell-editor[design][loading][cellFormatOptions]',
    templateUrl: './cell-editor.component.html',
    styleUrls: ['./cell-editor.component.scss'],
    // this is important for the change detection to work across components
    // makes this component use the same NgForm as the parent component
    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],
})
export class CellEditorComponent {
    @Input()
    public metrics: CellEditorMetrics | null;

    @Input()
    public loading: boolean;

    @Input()
    public design: CellDesign;

    @Input()
    public set cellFormatOptions(value: CellFormats[]) {
        this._cellFormatLookup = {};
        value.forEach((format) => (this._cellFormatLookup[format.id] = format));

        this._cellFormatOptions = value;
    }

    public get cellFormatOptions(): CellFormats[] {
        return this._cellFormatOptions;
    }

    public readonly maxFractionDigits = 9;

    public readonly controlNames = CONTROL_NAMES;

    private _cellFormatOptions: CellFormats[] = [];
    private _cellFormatLookup: IdLookup<CellFormats> = {};

    public constructor(public readonly notifier: NotifierService) {}

    public setIsDefault(_e: { checked: boolean }): void {
        if (this.design.cellFormatIsDefault) {
            this.design.cellFormatProperties = deepCloneDto(
                this._cellFormatLookup[this.design.cellFormatId].defaultProperties
            );
        }
    }

    public changeCellFormat(format: { value: string }): void {
        this.design.cellFormatIsDefault = true;
        this.design.cellFormatProperties = deepCloneDto(this._cellFormatLookup[format.value].defaultProperties);
    }

    public isPouch(design: CellDesign): design is CellDesignPouch {
        return this._cellFormatLookup[design.cellFormatId].geometry === CellGeometryEnum.pouch;
    }

    public isPrisma(design: CellDesign): design is CellDesignPrisma {
        return this._cellFormatLookup[design.cellFormatId].geometry === CellGeometryEnum.prisma;
    }

    public isCylinder(design: CellDesign): design is CellDesignCylinder {
        return this._cellFormatLookup[design.cellFormatId].geometry === CellGeometryEnum.cylinder;
    }

    public isPouchMetrics(
        design: CellDesign,
        metrics: CellEditorMetrics | null
    ): metrics is CellEditorPouchMetrics | null {
        return this._cellFormatLookup[design.cellFormatId].geometry === CellGeometryEnum.pouch;
    }

    public isPrismaMetrics(
        design: CellDesign,
        metrics: CellEditorMetrics | null
    ): metrics is CellEditorPrismaMetrics | null {
        return this._cellFormatLookup[design.cellFormatId].geometry === CellGeometryEnum.prisma;
    }

    public isCylinderMetrics(
        design: CellDesign,
        metrics: CellEditorMetrics | null
    ): metrics is CellEditorCylinderMetrics | null {
        return this._cellFormatLookup[design.cellFormatId].geometry === CellGeometryEnum.cylinder;
    }
}
