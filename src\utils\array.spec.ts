import { groupBy } from './array';

describe('groupBy', () => {
    it('should group numbers', () => {
        const datasets = [3, 2, 1, 2, 3, 3];

        const actual: Record<number, number[]> = groupBy(datasets, (data) => data);

        expect(actual[1]).toEqual([1]);
        expect(actual[2]).toEqual([2, 2]);
        expect(actual[3]).toEqual([3, 3, 3]);
    });

    it('should group strings', () => {
        const datasets = ['3', '2', '1', '2', '3', '3'];

        const actual: Record<string, string[]> = groupBy(datasets, (data) => data);

        expect(actual['1']).toEqual(['1']);
        expect(actual['2']).toEqual(['2', '2']);
        expect(actual['3']).toEqual(['3', '3', '3']);
    });

    it('should group by property', () => {
        type TestObject = {
            key: 'one' | 'two' | 'three';
        };

        const obj1: TestObject = {
            key: 'one',
        };
        const obj2: TestObject = {
            key: 'two',
        };
        const obj3: TestObject = {
            key: 'three',
        };

        const datasets = [obj3, obj2, obj1, obj2, obj3, obj3];

        const actual: Record<'one' | 'two' | 'three', TestObject[]> = groupBy(datasets, (data) => data.key);

        expect(actual.one).toEqual([obj1]);
        expect(actual.two).toEqual([obj2, obj2]);
        expect(actual.three).toEqual([obj3, obj3, obj3]);
    });

    it('should infer enum types', () => {
        enum TestEnum {
            One = 'one',
            Two = 'two',
            Three = 'three',
        }

        type TestObject = {
            key: TestEnum;
        };

        const obj1: TestObject = {
            key: TestEnum.One,
        };
        const obj2: TestObject = {
            key: TestEnum.Two,
        };
        const obj3: TestObject = {
            key: TestEnum.Three,
        };

        const datasets = [obj3, obj2, obj1, obj2, obj3, obj3];

        const actual: Record<TestEnum, TestObject[]> = groupBy(datasets, (data) => data.key);

        expect(actual.one).toEqual([obj1]);
        expect(actual.two).toEqual([obj2, obj2]);
        expect(actual.three).toEqual([obj3, obj3, obj3]);
    });
});
