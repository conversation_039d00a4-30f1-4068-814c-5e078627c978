import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { DecimalPipe } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { PrimeNgModule } from '@com/app/prime-ng';
import { SharedModule } from '@com/app/shared/shared.module';

import { NumberTwoFractionDigitsPipe } from '@com/pipe/decimal.pipe';
import { AnodeEditorComponent } from './anode-editor/anode-editor.component';
import { BalancingEditorComponent } from './balancing-editor/balancing-editor.component';
import { CathodeEditorComponent } from './cathode-editor/cathode-editor.component';
import { BomEditorComponent } from './bom-editor/bom-editor.component';
import { SummaryTableComponent } from './summary-table/summary-table.component';
import { CellDesignPageComponent } from './cell-design-page.component';
import { MaterialChartComponent } from './material-chart/material-chart.component';
import { MaterialsEditorComponent } from './materials-editor/materials-editor.component';
import { ElectrodePairEditorComponent } from './electrode-pair-editor/electrode-pair-editor.component';
import { EnergyContentEditorComponent } from './energy-content-editor/energy-content-editor.component';
import { CellEditorComponent } from './cell-editor/cell-editor.component';
import { ElectrolyteEditorComponent } from './electrolyte-editor/electrolyte-editor.component';
import { SocToolComponent } from './soc-tool/soc-tool.component';
import { OptimizationEditorComponent } from './optimization-editor/optimization-editor.component';
import { ParameterOptimizationComponent } from './parameter-optimization/parameter-optimization.component';
import { SwellingCalculationEditorComponent } from './swelling-calculation-editor/swelling-calculation-editor.component';
import { ToleranceEditorComponent } from './tolerance-editor/tolerance-editor.component';

@NgModule({
    declarations: [
        CellDesignPageComponent,
        CathodeEditorComponent,
        AnodeEditorComponent,
        BalancingEditorComponent,
        BomEditorComponent,
        ElectrodePairEditorComponent,
        EnergyContentEditorComponent,
        SummaryTableComponent,
        NumberTwoFractionDigitsPipe,
        MaterialChartComponent,
        MaterialsEditorComponent,
        CellEditorComponent,
        ElectrolyteEditorComponent,
        SocToolComponent,
        OptimizationEditorComponent,
        ParameterOptimizationComponent,
        SwellingCalculationEditorComponent,
        ToleranceEditorComponent,
    ],
    imports: [SharedModule, BrowserModule, BrowserAnimationsModule, FormsModule, ReactiveFormsModule, PrimeNgModule],
    providers: [DecimalPipe, NumberTwoFractionDigitsPipe],
})
export class CellDesignModule {}
