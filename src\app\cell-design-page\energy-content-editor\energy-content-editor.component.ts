import { Component, Input } from '@angular/core';
import { ControlContainer, NgModelGroup } from '@angular/forms';
import { NumberTwoFractionDigitsPipe } from '@com/pipe/decimal.pipe';

import { CellSummaryMetrics } from '@com/services/cell-design-metrics.service';
import { CellDesign } from '@com/services/cell-design.service';

const CONTROL_NAMES = {
    safety: 'safety',
    parallelCellsCount: 'parallel_cells_count',
    serialCellsCount: 'serial_cells_count',
    moduleCount: 'module_count',
    cellsPerModule: 'cellsPerModule',
} as const;

@Component({
    selector: 'com-energy-content-editor[design][metrics][loading]',
    templateUrl: './energy-content-editor.component.html',
    styleUrls: ['./energy-content-editor.component.scss'],

    // this is important for the change detection to work across components
    // makes this component use the same NgForm as the parent component
    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],
})
export class EnergyContentEditorComponent {
    @Input()
    public design: CellDesign | null;

    @Input()
    public loading: boolean;

    @Input()
    public set metrics(value: CellSummaryMetrics | undefined) {
        this._metrics = value;
        this.mapCapacityAndEnergyContentData();
    }

    public get metrics(): CellSummaryMetrics | undefined {
        return this._metrics;
    }

    public capacityAndEnergyContentData: { label: string; c10Value: string; c3Value: string }[] = [];

    public get parallelCellsCount(): number {
        return this.design?.parallelCellsCount ?? 0;
    }

    public set parallelCellsCount(value: number) {
        if (this.design) {
            this.design.parallelCellsCount = value;
        }
    }

    public get serialCellsCount(): number {
        return this.design?.serialCellsCount ?? 0;
    }

    public set serialCellsCount(value: number) {
        if (this.design) {
            this.design.serialCellsCount = value;
        }
    }

    public get moduleCount(): number {
        return this.design?.moduleCount ?? 0;
    }

    public set moduleCount(value: number) {
        if (this.design) {
            this.design.moduleCount = value;
        }
    }

    public get cellsPerModule(): number {
        if (this.design) {
            return (this.design.parallelCellsCount * this.design.serialCellsCount) / this.design.moduleCount;
        } else {
            return 0;
        }
    }

    public set cellsPerModule(value: number) {
        if (this.design) {
            this.design.moduleCount = (this.design.parallelCellsCount * this.design.serialCellsCount) / value;
        }
    }

    public readonly controlNames = CONTROL_NAMES;

    public readonly packDesignMaxValue = 1000;
    public readonly packDesignMinValue = 1;

    private _metrics: CellSummaryMetrics | undefined;

    public constructor(private _numberTwoFractionDigitsPipe: NumberTwoFractionDigitsPipe) {}

    private mapCapacityAndEnergyContentData(): void {
        this.capacityAndEnergyContentData = [];

        this.capacityAndEnergyContentData.push({
            label: $localize`:@@common.nominalVoltage:Nominelle Spannung`,
            c10Value: $localize`:@@common.volt: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeNominalVoltageC10
            )}:INTERPOLATION: V `, // Prettier formats this localization in the template to contain an empty space at the end
            c3Value: $localize`:@@common.volt: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeNominalVoltageC3
            )}:INTERPOLATION: V `, // Prettier formats this localization in the template to contain an empty space at the end
        });

        this.capacityAndEnergyContentData.push({
            label: $localize`:@@common.capacity:Kapazität`,
            c10Value: $localize`:@@common.amperHour: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellCapacityC10
            )}:INTERPOLATION: Ah `, // Prettier formats this localization in the template to contain an empty space at the end
            c3Value: $localize`:@@common.amperHour: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellCapacityC3
            )}:INTERPOLATION: Ah `, // Prettier formats this localization in the template to contain an empty space at the end
        });

        this.capacityAndEnergyContentData.push({
            label: $localize`:@@common.energyHold:Energienhalt`,
            c10Value: $localize`:@@common.wattHour: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellEnergyC10
            )}:INTERPOLATION: Wh`,
            c3Value: $localize`:@@common.wattHour: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellEnergyC3
            )}:INTERPOLATION: Wh`,
        });

        this.capacityAndEnergyContentData.push({
            label: $localize`:@@energyContent.energyDensityVolumentric:Energiedichte Volumetrisch`,
            c10Value: $localize`:@@common.watthourPerLitre: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellEnergyDensityVolumetricC10
            )}:INTERPOLATION: Wh/l`,
            c3Value: $localize`:@@common.watthourPerLitre: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellEnergyDensityVolumetricC3
            )}:INTERPOLATION: Wh/l`,
        });

        this.capacityAndEnergyContentData.push({
            label: $localize`:@@energyContent.energyDensityGravimetric:Energiedichte Gravimetrisch`,
            c10Value: $localize`:@@common.watthourPerKilogram: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellEnergyDensityGravimetricC10
            )}:INTERPOLATION: Wh/kg`,
            c3Value: $localize`:@@common.watthourPerKilogram: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellEnergyDensityGravimetricC3
            )}:INTERPOLATION: Wh/kg`,
        });

        this.capacityAndEnergyContentData.push({
            label: $localize`:@@common.materialPricePerKwH:Materialpreis pro kWh`,
            c10Value: $localize`:@@common.euroPerKilowattHour: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellPriceKwhC10
            )}:INTERPOLATION: €/kWh`,
            c3Value: $localize`:@@common.euroPerKilowattHour: ${this._numberTwoFractionDigitsPipe.transform(
                this.metrics?.safeCellPriceKwhC3
            )}:INTERPOLATION: €/kWh`,
        });
    }
}
