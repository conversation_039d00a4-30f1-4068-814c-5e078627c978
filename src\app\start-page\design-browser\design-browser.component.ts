import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subject, finalize, takeUntil } from 'rxjs';
import { FilterMatchMode } from 'primeng/api';

import { CellDesignWithId, CellDesignService } from '@com/services/cell-design.service';
import { CellDesignExportService } from '@com/services/cell-design-export.service';

type CellDesignForTable = Omit<CellDesignWithId, 'createdAt' | 'releasedAt'> & {
    createdAt?: Date;
    releasedAt?: Date;
};

const CELL_DESIGN_FIELDS = {
    designId: 'designId',
    name: 'name',
    createdAt: 'createdAt',
    createdBy: 'createdBy.name',
    projectName: 'projectName',
    projectState: 'projectState',
    releasedAt: 'releasedAt',
    releasedBy: 'releasedBy.name',
    released: 'released',
    partNumber: 'partNumber',
    description: 'description',
} as const;

const DESCENDING_ORDER: number = -1;

@Component({
    selector: 'com-design-browser',
    templateUrl: 'design-browser.component.html',
})
export class DesignBrowserComponent implements OnInit, OnDestroy {
    public designs: CellDesignForTable[];

    public selectedDesigns: CellDesignForTable[];

    public loading: boolean = true;

    public readonly releasedText = {
        yes: $localize`:@@common.released:Released`,
        no: $localize`:@@common.notReleased:Not Released`,
    } as const;

    public readonly fields = CELL_DESIGN_FIELDS;

    public readonly matchModes = FilterMatchMode;

    public readonly defaultOrder: number = DESCENDING_ORDER;

    private _ngUnsubscribe: Subject<void> = new Subject();

    public constructor(
        private _designService: CellDesignService,
        private _designExportService: CellDesignExportService
    ) {}

    public ngOnInit(): void {
        this.refresh();
    }

    public ngOnDestroy(): void {
        this._ngUnsubscribe.next();
        this._ngUnsubscribe.complete();
    }

    public refresh(): void {
        this.loading = true;

        this._designService
            // TODO: BE pagination & filtering
            .getAllCellDesigns()
            .pipe(
                takeUntil(this._ngUnsubscribe),
                finalize(() => (this.loading = false))
            )
            .subscribe((designs) => {
                this.designs = designs.map((design) => {
                    return {
                        ...design,
                        releasedAt: design.releasedAt ? new Date(design.releasedAt) : undefined,
                        createdAt: design.createdAt ? new Date(design.createdAt) : undefined,
                    };
                });
            });
    }

    public exportSelectedDesigns(): void {
        if (this.selectedDesigns) {
            const designIds = this.selectedDesigns.map((design) => design._id);
            this._designExportService.downloadExcelExportMultipleDesigns(designIds, 'export.xlsx').subscribe();
        }
    }
}
