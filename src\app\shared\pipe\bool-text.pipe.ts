import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'boolText',
})
export class BoolTextPipe implements PipeTransform {
    public transform(value: boolean, trueString?: string, falseString?: string): string {
        trueString = trueString ?? $localize`:@@common.yes:Yes`;
        falseString = falseString ?? $localize`:@@common.no:No`;

        return value ? trueString : falseString;
    }
}
