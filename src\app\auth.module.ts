import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { NgModule } from '@angular/core';
import {
    MSAL_INSTANCE,
    MSAL_GUARD_CONFIG,
    MsalService,
    MsalGuard,
    MsalBroadcastService,
    MsalGuardConfiguration,
    MsalInterceptorConfiguration,
    MSAL_INTERCEPTOR_CONFIG,
    ProtectedResourceScopes,
    MsalInterceptor,
} from '@azure/msal-angular';
import { IPublicClientApplication, PublicClientApplication, InteractionType } from '@azure/msal-browser';
import { ConfigService } from '@com/services/config.service';

export function MsalInstanceFactory(config: ConfigService): IPublicClientApplication {
    return new PublicClientApplication(config.data.msalConfig!);
}

export function MsalGuardConfigFactory(config: ConfigService): MsalGuardConfiguration {
    return {
        interactionType: InteractionType.Redirect,
        authRequest: {
            scopes: [`${config.data?.msalConfig?.auth?.clientId}/.default`],
        },
    };
}

export function MsalInterceptorConfigFactory(config: ConfigService): MsalInterceptorConfiguration {
    const protectedResourceMap = new Map<string, Array<string | ProtectedResourceScopes> | null>();
    protectedResourceMap.set(config.data.baseUrl?.replace(/\/+$/, '') + '/*', [
        `${config.data.msalConfig?.auth.clientId}/.default`,
    ]);

    return {
        interactionType: InteractionType.Popup,
        protectedResourceMap,
    };
}

@NgModule({
    imports: [],
    exports: [],
    declarations: [],
    providers: [
        {
            provide: HTTP_INTERCEPTORS,
            useClass: MsalInterceptor,
            multi: true,
        },
        {
            provide: MSAL_INSTANCE,
            useFactory: MsalInstanceFactory,
            deps: [ConfigService],
        },
        {
            provide: MSAL_GUARD_CONFIG,
            useFactory: MsalGuardConfigFactory,
            deps: [ConfigService],
        },
        {
            provide: MSAL_INTERCEPTOR_CONFIG,
            useFactory: MsalInterceptorConfigFactory,
            deps: [ConfigService],
        },
        MsalService,
        MsalGuard,
        MsalBroadcastService,
    ],
})
export class AuthModule {}
