import { HttpStatusCode } from '@angular/common/http';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastPositionEnum, ToastSeverityEnum } from '@com/app/prime-ng';
import { AuthService, UserRolesEnum } from '@com/services/auth.service';
import { AutocompleteService } from '@com/services/autocomplete.service';
import {
    CellDesignService,
    CellDesignWithId,
    CreateDesignResponse,
    UpdateDesignResponse,
} from '@com/services/cell-design.service';
import { OptimizationService, OptimizationSettings } from '@com/services/optimization.service';
import { deepCloneDto } from '@com/utils/object';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Observable, Subject, finalize, takeUntil } from 'rxjs';

const CONTROL_NAMES = {
    projectName: 'meta_project_name',
    name: 'meta_name',
    projectState: 'meta_project_state',
    partNumber: 'meta_part_number',
    description: 'meta_description',
    releaseStatus: 'meta_release_status',
} as const;

export enum MetaEditorTypeEnum {
    save = 'save',
    edit = 'edit',
    optimize = 'optimize',
}

@Component({
    selector: 'com-design-meta-editor[design][dialogVisible]',
    templateUrl: 'design-meta-editor.component.html',
    styleUrls: ['./design-meta-editor.component.scss'],
    providers: [ConfirmationService],
})
export class DesignMetaEditorComponent implements OnInit, OnDestroy {
    @ViewChild('generalForm')
    public form: NgForm;

    @Output()
    public dialogVisibleChange = new EventEmitter<boolean>();

    @Output()
    public designChange = new EventEmitter<CellDesignWithId>();

    @Input() public dialogType: MetaEditorTypeEnum = MetaEditorTypeEnum.save;

    @Input() public optimizationSettings: OptimizationSettings;

    @Input()
    public set dialogVisible(value: boolean) {
        this.editableDesign = deepCloneDto(this._design);
        this._dialogVisible = value;
    }

    public get dialogVisible(): boolean {
        return this._dialogVisible;
    }

    @Input()
    public set design(value: CellDesignWithId | null) {
        this.setDesign(value);
        this.dialogConfig = new Map<MetaEditorTypeEnum, { header: string; buttonLabel: string }>([
            [
                MetaEditorTypeEnum.save,
                {
                    header: $localize`:@@common.saveNewDesign:Save new design`,
                    buttonLabel: $localize`:@@common.save:Speichern`,
                },
            ],
            [
                MetaEditorTypeEnum.edit,
                {
                    header: $localize`:@@common.editingItem:Editing ${value?.designId}:INTERPOLATION: id`,
                    buttonLabel: $localize`:@@common.editMetadata:Edit Metadata`,
                },
            ],
            [
                MetaEditorTypeEnum.optimize,
                {
                    header: $localize`:@@createOptimization.createNewOptimizedDesign:Neues optimiertes Design erstellen`,
                    buttonLabel: $localize`:@@common.create:Create`,
                },
            ],
        ]);
    }

    public get design(): CellDesignWithId | null {
        return this._design;
    }

    public dialogConfig: Map<
        MetaEditorTypeEnum,
        {
            header: string;
            buttonLabel: string;
        }
    >;

    public editableDesign: CellDesignWithId | null;

    public controlNames = CONTROL_NAMES;

    public canRelease: boolean;

    public releaseOptions = [
        {
            name: $localize`:@@common.notReleased:Not Released`,
            value: false,
        },
        {
            name: $localize`:@@common.released:Released`,
            value: true,
        },
    ];

    public loading = false;

    public projectSuggestions: string[] = [];
    public projectStateSuggestions: string[] = [];

    private readonly _ngUnsubscribe: Subject<void> = new Subject();
    private _design: CellDesignWithId | null;
    private _dialogVisible: boolean;

    public constructor(
        private readonly _cellDesignService: CellDesignService,
        private readonly _authService: AuthService,
        private readonly _autocomplete: AutocompleteService,
        private readonly _confirmService: ConfirmationService,
        private readonly _router: Router,
        private _messageService: MessageService,
        private _optimizationService: OptimizationService
    ) {}

    public ngOnInit(): void {
        this.canRelease = this._authService.hasAnyRole(UserRolesEnum.release, UserRolesEnum.admin);
    }

    public ngOnDestroy(): void {
        this._ngUnsubscribe.next();
        this._ngUnsubscribe.complete();
    }

    public onConfirm(): void {
        if (this.dialogType === MetaEditorTypeEnum.optimize) {
            this.createNewOptimizedDesign();
        } else {
            this.saveDesign();
        }
    }

    public searchProjects(event: { query: string }): void {
        this._autocomplete
            .get('project', event.query)
            .pipe(takeUntil(this._ngUnsubscribe))
            .subscribe((suggestions) => (this.projectSuggestions = suggestions));
    }

    public searchProjectStates(event: { query: string }): void {
        this._autocomplete
            .get('project-state', event.query)
            .pipe(takeUntil(this._ngUnsubscribe))
            .subscribe((suggestions) => (this.projectStateSuggestions = suggestions));
    }

    public hideDialog = (): void => {
        this.dialogVisible = false;
        this.dialogVisibleChange.emit(false);
        this.loading = false;
    };

    private createNewOptimizedDesign = (): void => {
        if (!this.editableDesign || this.form.invalid) {
            return;
        }

        this._optimizationService
            .createOptimization(this.editableDesign, this.optimizationSettings)
            .subscribe((response) => {
                this._messageService.add({
                    key: ToastPositionEnum.topCenter,
                    severity: ToastSeverityEnum.success,
                    summary: $localize`:@@common.success:Success`,
                    detail: $localize`:@@toast.optimizationCreated: ${response.name}:INTERPOLATION: id`,
                });
                this._router.navigate(['/']);
                this.hideDialog();
            });
    };

    private saveDesign(): void {
        if (!this.editableDesign || this.form.invalid) {
            return;
        }

        this.loading = true;
        let observable: Observable<CreateDesignResponse | UpdateDesignResponse>;
        if (!this.editableDesign._id) {
            observable = this._cellDesignService.createCellDesign(this.editableDesign);
        } else {
            observable = this._cellDesignService.updateCellDesign(this.editableDesign);
        }

        observable.pipe(finalize(this.hideDialog)).subscribe((response) => {
            switch (response.status) {
                case HttpStatusCode.Ok:
                    this.setDesign(response.design);
                    this.designChange.emit(response.design);
                    break;

                case HttpStatusCode.Conflict:
                    this._confirmService.confirm({
                        message: $localize`:@@common.designsCannotBeEdited:Designs cannot be edited. Do you want to save your changes as a new design?`,
                        header: $localize`:@@common.warning:Achtung`,
                        icon: 'pi pi-exclamation-triangle',
                        accept: this.saveAsNewDesign,
                    });
                    break;

                case HttpStatusCode.SeeOther:
                    this._confirmService.confirm({
                        message: $localize`:@@common.designAlreadyExitsts:Design cannot be saved since it already exists. Do you want to open it?`,
                        header: $localize`:@@common.warning:Achtung`,
                        icon: 'pi pi-exclamation-triangle',
                        // TODO: Statically type-safe route navigation
                        accept: () => this._router.navigate(['/cell-design', response.newId]),
                    });
                    break;
                default:
                    throw `Unexpected status code encountered when saving design!`;
            }
        });
    }

    private showDialog = (): void => {
        this.dialogVisible = true;
        this.dialogVisibleChange.emit(true);
        this.loading = false;
    };

    private saveAsNewDesign = (): void => {
        if (this.editableDesign) {
            this.editableDesign = deepCloneDto(this.editableDesign);
            this.editableDesign._id = null!;
            this.editableDesign.designId = null!;
            this.editableDesign.name = null!;
            this.editableDesign.partNumber = null!;
            this.editableDesign.projectName = null!;
            this.editableDesign.projectState = null!;
            this.editableDesign.description = null!;
            this.editableDesign.released = false;

            this._design = this.editableDesign;
            this.dialogType = MetaEditorTypeEnum.save;

            this.showDialog();
        }
    };

    private setDesign(value: CellDesignWithId | null | undefined): void {
        if (this._design !== value) {
            this.editableDesign = value ? deepCloneDto(value) : null;
        }

        this._design = value!;
    }
}
