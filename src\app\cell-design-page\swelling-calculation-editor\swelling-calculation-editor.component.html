<div *ngIf="loading || !design" class="flex justify-content-center">
    <p-progressSpinner />
</div>

<div *ngIf="!swellingCalculationEnabled && !loading" class="p-error p-5">
    {{ messageForEnabling }}
</div>

<div *ngIf="design && swellingCalculationEnabled && !loading && metrics" class="grid align-items-stretch">
    <div class="col-12 lg:col-6">
        <p-panel i18n-header="@@swelling.parameters" header="Swelling Parameter">
            <div class="field grid">
                <label
                    [for]="controlNames.swellingDuringFormation"
                    class="col-12 mb-2 md:col-4 xl:mb-0"
                    i18n="@@swelling.swellingDuringFormation"
                >
                    Swelling während der Formierung
                </label>
                <div class="col-12 md:col-8 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.swellingDuringFormation"
                        [inputId]="controlNames.swellingDuringFormation"
                        [(ngModel)]="design.swellingDuringFormation"
                        mode="decimal"
                        suffix=" %"
                        [maxFractionDigits]="maxDigits"
                        [min]="0"
                        [required]="true"
                    ></p-inputNumber>
                    <label
                        i18n="@@swelling.fieldIsRequired"
                        *ngIf="design.swellingDuringFormation === null || design.swellingDuringFormation === undefined"
                        class="p-error mb-4 col-12"
                    >
                        Feld ist erforderlich.
                    </label>
                </div>
            </div>
            <div class="field grid">
                <label
                    [for]="controlNames.compressibilityOverPressureRange"
                    class="col-12 mb-2 md:col-4 xl:mb-0"
                    i18n="@@swelling.compressibility"
                    >Kompressibilität über den Druckbereich
                </label>
                <div class="col-12 md:col-8 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.compressibilityOverPressureRange"
                        [inputId]="controlNames.compressibilityOverPressureRange"
                        [(ngModel)]="design.compressibilityOverPressureRange"
                        mode="decimal"
                        suffix=" %"
                        [min]="0"
                        [maxFractionDigits]="maxDigits"
                        [required]="true"
                        [ngClass]="{ 'ng-invalid': true }"
                    ></p-inputNumber>
                    <label
                        i18n="@@swelling.fieldIsRequired"
                        *ngIf="
                            design.compressibilityOverPressureRange === null ||
                            design.compressibilityOverPressureRange === undefined
                        "
                        class="p-error mb-4 col-12"
                    >
                        Feld ist erforderlich.
                    </label>
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 md:col-4 xl:mb-0" i18n="@@swelling.layerFreeSpace">Freiraum Zelllagen </label>
                <div class="col-12 md:col-8 p-fluid pl-2">
                    <span i18n="@@common.units.micrometer"
                        >{{ metrics.cell.cellLayerDelta | numberTwoFractionDigits }} μm</span
                    >
                </div>
            </div>
        </p-panel>
    </div>

    <div class="col-12 lg:col-6">
        <p-panel i18n-header="@@swelling.swellingCalculations" header="Bestimmung des Zellbreathings">
            <div class="field grid">
                <label class="col-12 mb-2 md:col-6 xl:mb-0" i18n="@@swelling.totalBreathing"
                    >Gesamtes Breathing pro Schicht</label
                >
                <div class="col-12 md:col-6 p-fluid pl-2">
                    <span i18n="@@common.units.micrometer"
                        >{{ metrics.cellSwelling.totalBreathingPerLayer | numberTwoFractionDigits }} μm</span
                    >
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 md:col-6 xl:mb-0" i18n="@@swelling.stackBreathing">Stack-Atmung</label>
                <div class="col-12 md:col-6 p-fluid pl-2" i18n="@@common.units.percentages">
                    {{ metrics.cellSwelling.stackBreathing | numberTwoFractionDigits }}
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 md:col-6 xl:mb-0" i18n="@@swelling.cf3uncompressed"
                    >CF3 ungepresste Atmung</label
                >
                <div class="col-12 md:col-6 p-fluid pl-2" i18n="@@common.units.percentages">
                    {{ metrics.cellSwelling.cf3UncompressedBreathing | numberTwoFractionDigits }}
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 md:col-6 xl:mb-0" i18n="@@swelling.cf3compressed"
                    >CF3-Atmung mit Kompression
                </label>
                <div class="col-12 md:col-6 p-fluid pl-2" i18n="@@common.units.percentages">
                    {{ metrics.cellSwelling.cf3BreathingWithCompression | numberTwoFractionDigits }}
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 md:col-6 xl:mb-0" i18n="@@swelling.cf3Absolut"
                    >CF3 Absolute Atmung mit Kompression
                </label>
                <div class="col-12 md:col-6 p-fluid pl-2">
                    <span i18n="@@common.units.millimeter"
                        >{{
                            metrics.cellSwelling.cf3AbsolutBreathingWithCompression | numberTwoFractionDigits
                        }}
                        mm</span
                    >
                </div>
            </div>
            <div class="field grid">
                <label class="col-12 mb-2 md:col-6 xl:mb-0" i18n="@@swelling.freeSpace"
                    >Freiraum nach der Formierung
                </label>
                <div class="col-12 md:col-6 p-fluid pl-2">
                    <span i18n="@@common.units.micrometer"
                        >{{ metrics.cellSwelling.freeSpaceAfterFormation | numberTwoFractionDigits }} μm</span
                    >
                </div>
            </div>
        </p-panel>
    </div>
    <div class="pl-5 col-12 mt-5 com-text-warning text-xl break-word">
        <div i18n="@@swelling.assumptions" class="font-bold">Annahmen</div>
        <div i18n="@@swelling.constant" class="text-base">
            {{ "a = " + metrics.cellSwelling.swellingConstantA }}
        </div>
        <div i18n="@@swelling.constant" class="text-base">
            {{ "b = " + metrics.cellSwelling.swellingConstantB }}
        </div>
        <div i18n="@@swelling.constantPoreVolume" class="text-base">Konstantes Porenvolumen</div>
        <div i18n="@@swelling.expansionOfSCC" class="text-base">Expansion von SCC gemäß Dilatometrie: a * SoL^b</div>
        <div i18n="@@swelling.noCathode" class="text-base">Keine Expansion oder Schrumpfung der Kathode</div>
        <div i18n="@@swelling.sol" class="text-base">SoL_SCC = SoL_Anode bei SoC = 100%</div>
    </div>
</div>
