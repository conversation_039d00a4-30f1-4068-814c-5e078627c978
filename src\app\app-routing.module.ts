import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MsalGuard, MsalRedirectComponent } from '@azure/msal-angular';
import { CellDesignPageComponent } from './cell-design-page/cell-design-page.component';
import { StartPageComponent } from './start-page/start-page.component';

const routes: Routes = [
    { path: '', component: StartPageComponent, canActivate: [MsalGuard] },
    { path: 'cell-design', component: CellDesignPageComponent, canActivate: [MsalGuard] },
    { path: 'cell-design/:id', component: CellDesignPageComponent, canActivate: [MsalGuard] },
    { path: 'oidc', component: MsalRedirectComponent },
    {
        path: '**',
        redirectTo: '',
        pathMatch: 'full',
    },
];

@NgModule({
    imports: [RouterModule.forRoot(routes)],
    exports: [RouterModule],
})
export class AppRoutingModule {}
