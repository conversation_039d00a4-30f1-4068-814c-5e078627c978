{"locale": "en-x-source", "translations": {"chemicals.anode.prelithiationCapacity.input.label": " Prälithiierung ", "chemicals.common.developerMode.checkbox.label": " Entwicklermodus ", "chemicals.common.material.input.label": " Material {$INTERPOLATION} ", "chemicals.common.material.version.input.label": " Version Material {$INTERPOLATION} ", "chemicals.common.material.ratio.input.label": " Anteil Material {$INTERPOLATION} ", "swelling.fieldIsRequired": " <PERSON>ld ist erforderlich. ", "materials.percentages.error": "Die Summe der Prozentsätze muss 100 ergeben", "chemicals.common.qAim.input.label": " Ziel Qrev C/10 ", "chemicals.common.qAim1.input.label": " Q 1st Aim ", "qValues.minimumValue.error": " Sowohl Ziel Qrev C/10 als auch Ziel Q1st C/10 müssen mindestens {$INTERPOLATION} mAh/g betragen ", "qValues.comparison.error": " Ziel Q1st C/10 muss größer sein als Ziel Qrev C/10 ", "axisTitle.capacityAnode": " Capacity / mAh/g(AAM)", "axisTitle.voltage": " Voltage vs. Li / V", "tab.balancing": "Balancing", "balancing.npRatioFirst.input.label": " N/P Verhältnis Formierung ", "balancing.npRatioRev.label": "N/P Verhältnis Reversibel", "common.voltage": "Spannung", "balancing.uMin.input.label": " <PERSON><PERSON> ", "balancing.uMax.input.label": " Umax ", "balancing.hysteresis.label": "Hysterese", "common.milliVolts": " {$INTERPOLATION} mV ", "common.warning": "Achtung", "axisTitle.capacityCathode": " Capacity / mAh/g(CAM)", "axisTitle.cellPotential": " Cell potential / V", "bom.input.dataSet.label": "Data Set", "bom.input.scrapMaterial.label": " Ausschuss Material ", "cellDesign.topbar.designId": "DesignID: {$INTERPOLATION}", "cellDesign.topbar.name": "Name: {$INTERPOLATION}", "cellDesign.topbar.releaseStatus": "Freigabestatus: {$INTERPOLATION}", "cellDesign.topbar.partNumber": "Part Nummer: {$INTERPOLATION}", "cellDesign.topbar.capacityAh": "Kapazität: {$INTERPOLATION} Ah", "cellDesign.topbar.energyContentWh": "Energienhalt: {$INTERPOLATION} Wh", "cellDesign.topbar.energyDensityVolumentric": "Energiedichte Volumetrisch: {$INTERPOLATION} Wh/l", "cellDesign.topbar.energyDensityGravimetric": "Energiedichte Gravimetrisch: {$INTERPOLATION} Wh/kg", "cellDesign.topbar.packageWeight": "Gesamte Zelle: {$INTERPOLATION} g", "common.export": "Export", "common.save": "Speichern", "common.genericErrorMessage": " Ein Fehler ist aufgetreten: {$INTERPOLATION} ", "common.validationErrorTitle": "Design Validierungsfehler", "common.validationErrorMessage": " Ihr Design enthält Validierungsfehler. Bitte beheben Sie die mit * markier<PERSON>hler, um Metriken zu berechnen. ", "tab.chemicalDesign": "Chemie Design", "tab.cathode": "Kathode", "tab.anode": "Anode", "tab.cellDesign": "Zell Design", "tab.materials": "Materialien", "tab.electrodePair": "Elektrodenpaar", "tab.cell": "<PERSON><PERSON>", "tab.electrolyte": "Elektrolyt", "tab.cellSpecs": "Zell Specs", "tab.energyContent??": "Energiegehalt", "tab.bom": "BOM", "tab.designAnalysis": "Design Analysis", "tab.socTool": "SOC Tool", "swelling.swellingCalculation": "Bestimmung des Zellbreathings", "tab.toleranceAnalysis": "Toleranzanalyse", "tab.cellOptimization": "Zell Optimierung", "tab.createOptimization": "<PERSON><PERSON>elle Optimierung", "tab.parameterOptimization": "Parameter Optimierung", "common.released": "Released", "common.notReleased": "Not Released", "cell.cellGeometry": "Zellgeometrie", "cell.cellFormat": "Zellformat", "cell.editFormat": "Format bearbeiten?", "cell.hasTwoSubstacks": "Zusammenbau aus 2 Substacks?", "cell.celltype.label": "Zelltyp", "cell.cellType.pouch": "Pouch", "cell.cellType.prisma": " Prismatisch ", "cell.cellLength": "<PERSON><PERSON>", "cell.cellWidth": "Zell B<PERSON>", "cell.cellThickness": "<PERSON><PERSON>", "cell.cellVolume": "Zellvolumen", "common.units.mililitre": "{$INTERPOLATION} ml", "cell.caseWeight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (inkl. Tabs)", "cell.coatingSurfaces": "Beschichtungsflächen", "cell.coatingLengthCathode": "Beschichtungslänge Kathode", "cell.coatingWidthCathode": "Beschichtungsbreite Kathode", "cell.coatingAreaCathode": "Beschichtungsfläche Kathode", "common.units.squareMillimeters": "{$INTERPOLATION} mm²", "cell.coatingLengthAnode": "Beschichtungslänge Anode", "cell.coatingWidthAnode": "Beschichtungsbreite Anode", "cell.coatingAreaAnode": "Beschichtungsfläche Anode", "cell.coatingLengthSeparator": "Beschichtungslänge Separator", "cell.coatingWidthSeparator": "Beschichtungsbreite Separator", "cell.coatingAreaSeparator": "Beschichtungsfläche Separator", "cell.cellLayerCalculation": "Zelllagenberechnung", "cell.housingThickness": "Pouch/Gehäuse-Wandstärke", "cell.swellingBuffer": "Swelling-Vorhalt", "cell.electrolyteSwelling": "Elektrolytswelling", "common.units.millimeter": "{$INTERPOLATION} mm", "cell.assemblyClearance": "Montagefreiraum", "cell.maxTotalLayerThickness": "<PERSON><PERSON><PERSON><PERSON>", "cell.totalLayerThickness": "Gesamtdicke Z<PERSON>lagen", "cell.layerFreeSpace": "Freiraum Zelllagen", "common.units.micrometer": "{$INTERPOLATION} μm", "cell.layerFreeSpaceOverThickness": "Freiraum Zelllagen / Dick<PERSON>lage", "cell.numberOfLayersCathode": "<PERSON><PERSON><PERSON> Lagen Kathode", "cell.numberOfLayersAnode": "<PERSON><PERSON><PERSON> Lagen Anode", "cell.numberOfActiveLayers": "Anzahl aktive Lagen", "cell.chargePerLayerC10": "Ladung pro Zelllage C/1O", "common.units.amperHours": "{$INTERPOLATION} Ah", "cell.energyPerLayerC10": "Energie pro Zelllage C/1O", "common.units.wattHours": "{$INTERPOLATION} Wh", "cell.chargePerLayerC3": "Ladung pro Zelllage C/3", "cell.energyPerLayerC3": "Energie pro Zelllage C/3", "cell.numberOfLayersSeparator": "<PERSON><PERSON><PERSON> Lagen Separator", "cell.totalAreaSeparator": "Gesamtfläche Separator", "cell.cellType.cylinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cell.cellDiameter": "<PERSON><PERSON>", "cell.cellHeight": "<PERSON><PERSON>", "cell.cellCoreDiameter": "Zellkern Durchmesser", "cell.cellLayerDiameterMax": "Max. Gesamtdurchmesser Zelllagen", "cell.cellLayerDiameterTotal": "Gesamtdurchmesser Zelllagen", "cell.cathodeLengthTotal": "Gesamtlänge Kathode", "cell.anodeLengthTotal": "Gesamtlänge Anode", "cell.anodeOverhang": "Anodenüberstand", "cell.cathodeWindingCount": "<PERSON><PERSON>hl Windungen Kathode", "cell.anodeWindingCount": "<PERSON><PERSON>hl Windungen Anode", "cell.separatorWindingCount": "<PERSON><PERSON>hl Windungen Separator", "electrodePair.calendarDates.label": "Kalenderdaten", "electrodePair.cathodeDensity.input.label": " Dichte Kathode ", "electrodePair.anodeDensity.input.label": " Dichte Anode ", "electrodePair.proposityCathode.label": " Porosität Kathode ", "common.percentageNumber": " {$INTERPOLATION} % ", "electrodePair.proposityAnode.label": " Porosität Anode ", "electrodePair.coatingAreaLayerThickness.label": "Beschichtungsflächendichte und Schichtdicke", "electrodePair.balancingAnodeCathode.label": " Balancing (Anode/Kathode) ", "electrodePair.chargeDensityCathode.input.label": " Ladungsdichte Kathode ", "electrodePair.chargeDensityAnode.label": " Ladungsdichte Anode ", "common.milliAmperHourPerSquareCentiMeter": " {$INTERPOLATION} mAh/cm² ", "electrodePair.loadingCathode.label": " Beladung Kathode ", "common.milliGramPerSquareCentiMeter": " {$INTERPOLATION} mg/cm² ", "electrodePair.loadingAnode.label": " Beladung Anode ", "electrodePair.coatingThicknessCathode.label": " Beshichtungsdicke Kathode ", "common.microMeter": " {$INTERPOLATION} µm ", "electrodePair.coatingThicknessAnode.label": " Beshichtungsdicke Anode ", "electrodePair.aluminumFoilThickness.input.label": " <PERSON><PERSON> ", "electrodePair.copperFoilThickness.input.label": " <PERSON><PERSON>folie ", "electrodePair.separatorThickness.input.label": " <PERSON><PERSON> ", "electrodePair.cellLayerThickness.label": "<PERSON><PERSON>", "electrodePair.electrodeThickness.label": "Elektrodendicken", "electrodePair.cathodeThickness.label": "<PERSON><PERSON>", "electrodePair.anodeThickness.label": "<PERSON><PERSON>", "common.amountOfElectrolyte": "Elektrolytmenge", "common.poreVolume": "Porenvolumen", "common.units.mililitrePerAmperHour": "{$INTERPOLATION} ml/Ah", "electrolyte.suggestedAmountOfElectrolyte": " Vorschlag für Elektrolytmenge (mit Puffer für Formierung) ", "common.aging": "Alterung", "electrolyte.electrolyteConsumptionThroughSeiFormation": " Elektrolytverbrauchen durch SEI Bildung ", "electrolyte.estimatedFromSeiReaction": "Abgeschätzt aus SEI-Reaktion", "electrolyte.firstCycleEfficiency": "First Cycle Efficiency", "common.outOfBalancing": "aus Balancing", "electrolyte.seiGrowthPerCharge": "SEI Wachstum pro Ladung", "common.units.nanometersPerAmperhour": "{$INTERPOLATION} nm/Ah", "electrolyte.activeSurfaceAnode": "Aktive Oberfläche Anode", "common.units.squareMeter": "{$INTERPOLATION} m²", "common.informationFromManufacturer": "aus Herstellerangaben", "electrolyte.warning.message": " Erste Abschätzung Electrolytverbrauch durch SEI Reaktion ", "electrolyte.warning.source": " (Quelle: {$INTERPOLATION}) ", "electrolyte.warning.request": " Hier brauchen wir noch zusätzlichen Input ! ", "common.cellDimensions": "Zelldimensionen", "energyContent.cellWeightOverall.label": " Gewicht gesamte Zelle ", "common.gram": " {$INTERPOLATION} g ", "energyContent.cellVolumeOverall.label": " Volume gesam<PERSON> Z<PERSON> ", "common.litre": " {$INTERPOLATION} l ", "energyContent.priceCell.label": " Materialpreis pro Celle ", "common.euro": " {$INTERPOLATION} € ", "energyContent.capacityAndEnergyContent": "Kapazität und Energiegehalt", "common.safety": "Sicherheit", "common.cRate10Symbol": "C/10", "common.cRate3Symbol": "C/3", "common.packDesign": "Pack Design", "energyContent.parallelCellsCount.label": "Zellen parallel", "energyContent.serialCellsCount.label": "<PERSON><PERSON><PERSON>", "common.moduleCount": "<PERSON><PERSON><PERSON>", "energyContent.cellsPerModule.label": "Zellen pro Modul", "common.cellsTotal": "<PERSON><PERSON><PERSON> g<PERSON>t", "common.nominalVoltage": "<PERSON><PERSON><PERSON>", "common.volt": " {$INTERPOLATION} V ", "common.capacity": "Kapazität", "common.amperHour": " {$INTERPOLATION} Ah ", "common.energyHold": "Energienhalt", "common.kiloWatHour": " {$INTERPOLATION} kWh ", "common.packingWeight": "Packgewicht", "common.kilogram": " {$INTERPOLATION} kg ", "common.price": " Pre<PERSON> ", "common.wattHour": " {$INTERPOLATION} Wh", "energyContent.energyDensityVolumentric": "Energiedichte Volumetrisch", "common.watthourPerLitre": " {$INTERPOLATION} Wh/l", "energyContent.energyDensityGravimetric": "Energiedichte Gravimetrisch", "common.watthourPerKilogram": " {$INTERPOLATION} Wh/kg", "common.materialPricePerKwH": "Materialpreis pro kWh", "common.euroPerKilowattHour": " {$INTERPOLATION} €/kWh", "common.density": "<PERSON><PERSON><PERSON>", "common.anode": "Anode", "materials.activeMaterialWeight.input.label": " Aktivmaterial ", "materials.binderWeight.input.label": " Binder ", "materials.conductiveAdditive.input.label": " Conductive Additive Materialien ", "materials.currentCollector.input.label": " Stromableiter ", "materials.weightAfterPrelithiation.header": " Gewichtsprozente nach Prälithiierung ", "materials.prelithiationProcess.label": " Prälithiierungsprozess ", "materials.nanoscale.label": "Nanoscale", "materials.group14.label": "Group 14", "materials.lithiumWeight.label": "Lithium", "common.anodeDensity": "Dichte Anode", "materials.anode.fullCellUtilization": " Ausnutzung Anode Vollzelle ", "materials.anode.halfCellUtilization": " Ausnutzung An<PERSON> ", "common.cathode": "Kathode", "common.cathodeDensity": "Dichte Kathode", "materials.cathode.fullCellUtilization": " Ausnutzung Kathode Vollzelle ", "materials.cathode.halfCellUtilization": " Ausnutzung Kathode Hal<PERSON>le ", "common.electrolyteSeparator": " Electrolyt + Separator ", "materials.electrolyte.input.label": " Elektrolyt ", "materials.separator.input.label": " Separator ", "common.porosity": "Porosität", "createOptimization.noOptimizationMessage": "Optimierung der Anodenmaterialm ischung nicht verfügbar.", "createOptimization.objectiveFunction": "Zielfunktion", "createOptimization.optimizationObjective": "Optimierungsziel", "createOptimization.cellCostValidation": " <PERSON><PERSON> Z<PERSON> verfügbar. Bitte lege alle Preise im BOM-Editor fest. ", "createOptimization.swellingValidation": " Die Optimierung des Swelling-Ziels ist nicht möglich. Bitte aktiviere Swelling. ", "createOptimization.optimizationTarget": "Optimierungsfokus", "createOptimization.optimizationAlgorithm": "Optimierungsalgorithmus", "createOptimization.parameterSet": "Parametersatz", "createOptimization.optimizationVariables": "OptimierungSvariablen", "createOptimization.lowerLimit": "Unteres Limit", "createOptimization.upperLimit": "Oberes Limit", "createOptimization.stepSize": "Limit <PERSON>hrittgrö<PERSON>", "createOptimization.anodeWeightValidation": "Die Optimierung des Anodengewichts ist nicht möglich. Nur Mischungen mit Material der Gruppe 14 SCC55 sind zulässig. ", "createOptimization.cathodeWeightValidation": " Eine Optimierung des Kathodengewichts ist nicht möglich. Um dies zu ermöglichen, müssen Sie Kathodenmaterialien mischen. ", "createOptimization.lowerLimitValidation": " Minimalwert muss unter Höchstwert liegen ", "createOptimization.stepSizeValidation": " Schrittgr<PERSON>ße muss kleiner sein als die Differenz zwischen Ober und Untergrenze ", "createOptimization.advancedSettings": "Erweiterte Einstellungen", "createOptimization.numInitialPoints": "Anzahl der Anfangspunkte", "createOptimization.numIterations": "Anzahl der Iterationen", "createOptimization.createNewOptimizedDesign": "Neues optimiertes Design erstellen", "createOptimization.warningMessageGrid": "Diese Optimierungskonfiguration kann länger dauern. Reduzieren Sie den Bereich der Variablenlimits (unteres und oberes Limit) oder erhöhen Sie die Schrittgröße, um den Vorgang zu beschleunigen.", "createOptimization.warningMessageBayesian": "Diese Optimierungskonfiguration kann länger dauern. Um den Prozess zu beschleunigen, reduzieren Sie die Anzahl der Anfangspunkte oder verringern Sie die Anzahl der Iterationen.", "parameterOptimization.noOptimizationMessage": "<PERSON><PERSON><PERSON> dieses Zell Design steht aktuell keine Optimierung zur Verfügung.", "parameterOptimization.optimizationRunning": "Optimierung im Gange. <PERSON><PERSON><PERSON> dieser abgeschlossen ist, werden die Ergebnisse hier angezeigt. <PERSON>te warte.", "parameterOptimization.loading": "Laden", "parameterOptimization.timeRemaining": "{$START_TAG_SPAN}Geschätzte verbleibende Zeit: {$INTERPOLATION}{$CLOSE_TAG_SPAN}", "parameterOptimization.optimizationFailed": "{$START_TAG_SPAN}Optimierung fehlgeschlagen. Bitte erstelle eine neue.{$CLOSE_TAG_SPAN}", "parameterOptimization.optimizationSettings": "Optimierungseinstellungen", "parameterOptimization.optimizationOutcome": "Optimierungsergebnis", "parameterOptimization.beforeOptimization": "Vor der Optimierung", "parameterOptimization.afterOptimization": "Nach der Optimierung", "parameterOptimization.cellKPI": "Zell-KPI", "parameterOptimization.efficiencyGain": "Effizienzsteigerung", "parameterOptimization.chartType": "Diagrammtyp", "parameterOptimization.optimizationVariable": "OptimierungSvariable", "toast.optimizationRunningTitle": "Optimierungsprozess läuft noch", "toast.optimizationRunningDesc": "Optimierung noch nicht abgeschlossen. Bitte warte bis der Optimierungsprozess abgeschlossen ist.", "toast.optimizationCompletedTitle": "Optimierungsprozess abgeschlossen", "toast.optimizationCompletedDesc": "Zelldesign wurde aktualisiert.", "common.soc": "SoC", "common.voltageLithium": "<PERSON><PERSON><PERSON> vs. <PERSON>", "common.charge": "Laden", "common.discharge": "Entladen", "common.socMaterial": "SoC Material {$INTERPOLATION}", "swelling.parameters": "Swelling Parameter", "swelling.swellingDuringFormation": " Swelling während der Formierung ", "swelling.compressibility": "Kompressibilität über den Druckbereich ", "swelling.layerFreeSpace": "Freiraum Zelllagen ", "swelling.swellingCalculations": "Bestimmung des Zellbreathings", "swelling.totalBreathing": "Gesamtes Breathing pro <PERSON><PERSON><PERSON>", "swelling.stackBreathing": "Stack-<PERSON><PERSON><PERSON>", "common.units.percentages": " {$INTERPOLATION} ", "swelling.cf3uncompressed": "CF3 ungepresste Atmung", "swelling.cf3compressed": "CF3-At<PERSON><PERSON> mit Kompression ", "swelling.cf3Absolut": "CF3 Absolute Atmung mit Kompression ", "swelling.freeSpace": "Freiraum nach der Formierung ", "swelling.assumptions": "<PERSON><PERSON><PERSON>", "swelling.constant": " {$INTERPOLATION} ", "swelling.constantPoreVolume": "Konstantes Porenvolumen", "swelling.expansionOfSCC": "Expansion von SCC gemäß Dilatometrie: a * SoL^b", "swelling.noCathode": "Keine Expansion oder Schrumpfung der Kathode", "swelling.sol": "SoL_SCC = SoL_Anode bei SoC = 100%", "swelling.noDesign": "Swelling calculation is disabled because no design data is available. Please provide a valid design.", "swelling.wrongFormatAndMaterial": "Die Bestimmung des Zellbreathings ist nicht verfügbar, da das Zellformat nicht CF3 ist und das Anodenmaterial nicht korrekt ist. Bitte verwenden Sie das CF3-Format und stellen Sie sicher, dass das Anodenmaterial Group 14 SCC ist.", "swelling.wrongFormat": "Die Bestimmung des Zellbreathings ist nur für das CF3-Zellformat verfügbar. Bitte aktualisieren Sie das Zellformat, um die Berechnungen zu ermöglichen.", "swelling.wrongMaterial": "Die Bestimmung des Zellbreathings erfordert, dass das Anodenmaterial Group 14 SCC ist. Bitte wählen Sie Group 14 SCC als Anodenmaterial aus.", "swelling.wrongMaterialBlend": "Die Bestimmung des Zellbreathings erfordert, dass mindestens ein Anodenmaterial in einem Blend Group 14 SCC ist. Bitte stellen <PERSON>, dass mindestens eines der ausgewählten Materialien Group 14 SCC ist.", "createTolerance.noToleranceMessage": "Toleranzanalyse nicht verfügbar.", "createTolerance.parameterSet": "Parametersatz", "createTolerance.valueType": "Wertetyp", "createTolerance.valueTypeTooltip": "Absolute: Minimum und Maximum sind absolute Werte. Relativ: Minimum und Maximum sind Differenzen zum Standardwert. Relative Prozent: Minimum und Maximum sind prozentuale Abweichungen vom Standardwert. Alle Werte werden mit dem Standardwert verglichen, um die Toleranz zu bestimmen.", "createTolerance.minValueTooHigh": " Minimalwert muss kleiner als der Standardwert sein. ", "createTolerance.maxValueTooLow": " Maximalwert muss größer als der Standardwert sein. ", "createTolerance.negativeValueForAbsolute": " <PERSON><PERSON>r absolute Werte sind nur positive Z<PERSON>en erlaubt. ", "createTolerance.qValuesWarning": " Ziel Q1st C/10 muss größer sein als Ziel Qrev C/10, aber in dieser Toleranzkonfiguration ist das nicht garantiert ", "createTolerance.toleranceVariables": "Toleranzvariablen", "createTolerance.minimumValue": "Minimum Wert", "createTolerance.standardValue": "Standard Wert", "createTolerance.maximumValue": "Maximum Wert", "createTolerance.missingStandardValue": " Der Wert für diese Toleranzvariable ist im Zelldesign nicht angegeben. Um die Toleranz zu berechnen, müssen Sie diesen Wert zuerst festlegen. ", "createTolerance.calculateTolerance": "To<PERSON><PERSON><PERSON> be<PERSON>", "createTolerance.calculating": "Toleranz wird berechnet...", "createTolerance.variableResults": "Variablenergebnisse", "createTolerance.variableCategory": "Variablenkategorie", "createTolerance.resultsFor": "Ergebnisse für:", "createTolerance.standardValueResult": "Standard Value", "createTolerance.chainedResults": "Verkettete Ergebnisse", "createTolerance.histogramResults": "Verteilungsanalyse", "createTolerance.histogramMetric": "<PERSON><PERSON>", "createTolerance.binCount": "<PERSON><PERSON><PERSON> der <PERSON>s", "createTolerance.binCountValue": "{$INTERPOLATION} Bins", "createTolerance.valueTypeAbsolute": "Absolut", "createTolerance.valueTypeRelative": "Relativ", "createTolerance.valueTypeRelativePercentage": "<PERSON><PERSON><PERSON>", "createTolerance.chartTitle": "Verteilung der Werte", "createTolerance.chartXAxis": "Wertebereich", "createTolerance.chartYAxis": "Häufigkeit", "common.name": "Name", "common.projectName": "Projektname", "common.projectState": "Musterstand", "common.partNumber": "Part Nummer", "common.description": "Beschreibung", "common.releaseStatus": "Frei<PERSON>ben<PERSON><PERSON>", "common.releasedDate": "Freigabendatum", "common.releasedBy": "<PERSON><PERSON><PERSON><PERSON> von", "common.cancel": "Cancel", "common.saveNewDesign": "Save new design", "common.editingItem": "Editing {$INTERPOLATION} id", "common.editMetadata": "<PERSON>", "common.create": "Create", "common.success": "Success", "toast.optimizationCreated": " {$INTERPOLATION} id", "common.designsCannotBeEdited": "Designs cannot be edited. Do you want to save your changes as a new design?", "common.designAlreadyExitsts": "Design cannot be saved since it already exists. Do you want to open it?", "common.errorWithMessage": "Error: {$message}", "common.errorWithCode": "Error Code: {$statusCode}, Message: {$message}", "common.unexpectedError": "Unexpected Error", "common.yes": "Yes", "common.no": "No", "common.startNewDesign": "Start new Design", "common.design": "Design", "common.createdAt": "Erstelldatum", "common.createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "common.confirmation": "Confirmation", "common.open": "Open", "common.editOrRelease": "Edit Metadata/Release", "common.delete": "Delete", "common.deleteConfirmationMessage": "Are you sure that you want to delete {$name}?", "common.confirm": "Confirm", "common.itemHasBeenDeleted": "{$PH} has been deleted successfully!"}}