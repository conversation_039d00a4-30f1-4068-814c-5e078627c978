import { Component, Input } from '@angular/core';
import { ControlContainer, NgModelGroup } from '@angular/forms';
import { BAR_CHART_OPTIONS } from '@com/const';

import { ElectrodePairMetrics } from '@com/services/cell-design-metrics.service';
import { CellDesign } from '@com/services/cell-design.service';
import { deepCloneDto } from '@com/utils/object';

const CONTROL_NAMES = {
    cathodeCalanderDensity: 'cathode_calander_density',
    anodeCalanderDensity: 'anode_calander_density',
    cathodeArealCapacity: 'cathode_areal_capacity',
    thicknessAluminiumFoil: 'thickness_aluminium_foil',
    thicknessCopperFoil: 'thickness_copper_foil',
    thicknessSeparator: 'thickness_separator',
} as const;

@Component({
    selector: 'com-electrode-pair-editor',
    templateUrl: './electrode-pair-editor.component.html',

    // this is important for the change detection to work across components
    // makes this component use the same NgForm as the parent component
    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],
})
export class ElectrodePairEditorComponent {
    @Input()
    public design: CellDesign | null;

    @Input()
    public metrics: ElectrodePairMetrics | undefined;

    @Input()
    public loading: boolean;

    public readonly controlNames = CONTROL_NAMES;

    public readonly maxDigits = 4;

    // chart.js have the chart options defined as any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public options: any = {
        ...deepCloneDto(BAR_CHART_OPTIONS.rootOptions),
        ...deepCloneDto(BAR_CHART_OPTIONS.horizontal),
        plugins: { ...deepCloneDto(BAR_CHART_OPTIONS.bottomLegendNoClick) },
        scales: { ...deepCloneDto(BAR_CHART_OPTIONS.stackedScales) },
    };
}
