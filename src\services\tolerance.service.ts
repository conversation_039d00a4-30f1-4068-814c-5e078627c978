import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { ConfigService } from './config.service';
import { CellDesign } from './cell-design.service';

export interface ToleranceVariable {
    id: string;
    name: string;
    unit: string;
    lowerLimit?: number;
    standardValue?: number;
    upperLimit?: number;
}

export interface ToleranceResult {
    id: string;
    name: string;
    minValue: number;
    standardValue?: number;
    maxValue: number;
    unit: string;
    allValues?: number[]; // Array of all values for histogram analysis
}
export interface ToleranceResponse {
    variables: ToleranceVariable[];
}

export interface ToleranceCalculationResponse {
    chainedResult: ToleranceResult[] | null;
    variableResults: {
        [key: string]: ToleranceResult[];
    };
}

export enum ToleranceValueTypeEnum {
    absolute = 'absolute',
    relative = 'relative',
    relativePercentage = 'relativePercentage',
}

export type ToleranceSettings = {
    variables: ToleranceVariable[];
    valueType: ToleranceValueTypeEnum;
};

@Injectable({
    providedIn: 'root',
})
export class ToleranceService {
    private constructor(private _config: ConfigService, private _http: HttpClient) {}

    public getToleranceVariables(): Observable<ToleranceVariable[]> {
        return this._http
            .get<ToleranceResponse>(`${this._config.data.baseUrl}/api/tolerance-settings/`)
            .pipe(map((response) => response.variables));
    }

    public calculateTolerance(cellDesign: CellDesign): Observable<ToleranceCalculationResponse> {
        return this._http.post<ToleranceCalculationResponse>(`${this._config.data.baseUrl}/api/tolerance/`, cellDesign);
    }
}
