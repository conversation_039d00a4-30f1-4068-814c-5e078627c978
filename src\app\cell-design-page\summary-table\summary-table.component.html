<p-table [columns]="headers" [value]="mappedRows" [tableStyle]="{ 'min-width': '50rem' }">
    <ng-template pTemplate="header" let-columns>
        <tr>
            <th *ngFor="let header of columns; index as i" [ngClass]="{ highlighted: highlightedColumns.includes(i) }">
                {{ header }}
            </th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-rowData>
        <tr [ngClass]="{ highlighted: rowData.isHighlighted }">
            <ng-container *ngFor="let cell of rowData.cells; index as i">
                <td
                    [ngClass]="{ highlighted: highlightedColumns.includes(i) }"
                    *ngIf="cell.editable; else nonEditableCell"
                >
                    <com-inplace-edit
                        [value]="cell.editable.value"
                        [showUndo]="cell.editable.hasBeenEdited"
                        (valueChange)="$event != null ? onCellValueChange(cell, $event) : onCellValueReset(cell)"
                    >
                        <ng-template #display let-value="value">
                            <ng-container
                                *ngTemplateOutlet="cellValue; context: { value: value, unit: cell.unit }"
                            ></ng-container>
                        </ng-template>
                        <ng-template #edit let-value="value" let-setValue="setValue">
                            <p-inputNumber
                                comPrimeLocale
                                [ngModel]="value"
                                (ngModelChange)="setValue($event)"
                                [maxFractionDigits]="2"
                                [min]="cell.editable.min"
                                [max]="cell.editable.max"
                                mode="decimal"
                                [suffix]="cell.unit ? ' ' + cell.unit : ''"
                                styleClass="table-input"
                            ></p-inputNumber>
                        </ng-template>
                    </com-inplace-edit>
                </td>

                <ng-template #nonEditableCell
                    ><td [ngClass]="{ highlighted: highlightedColumns.includes(i) }">
                        <ng-container
                            *ngTemplateOutlet="cellValue; context: { value: cell.value, unit: cell.unit }"
                        ></ng-container>
                    </td>
                </ng-template>
            </ng-container>
        </tr>
    </ng-template>
</p-table>

<ng-template #cellValue let-value="value" let-unit="unit">
    <span
        >{{
            isNaN(value) ? value : unit === "g" ? (value | number : "1.0-3") : (value | numberTwoFractionDigits)
        }}&nbsp;{{ value != null ? unit : "" }}</span
    >
</ng-template>
