// The type for left/right here is more complex and does not bring a lof of value for this function
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function deepEquals(left: any, right: any): boolean {
    if (left instanceof Object && right instanceof Object) {
        const leftKeys = Object.keys(left);
        const rightKeys = Object.keys(right);
        if (leftKeys.length !== rightKeys.length) {
            return false;
        }

        for (const key of leftKeys) {
            if (!rightKeys.includes(key)) {
                return false;
            }

            if (!deepEquals(left[key], right[key])) {
                return false;
            }
        }

        return true;
    } else if (Array.isArray(left) && Array.isArray(right)) {
        if (left.length !== right.length) {
            return false;
        }

        for (let i = 0; i < left.length; ++i) {
            if (!deepEquals(left[i], right[i])) {
                return false;
            }
        }

        return true;
    } else {
        return left === right;
    }
}
