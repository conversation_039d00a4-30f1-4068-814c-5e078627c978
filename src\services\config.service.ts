import { Injectable } from '@angular/core';
import { Configuration } from '@azure/msal-browser';
import { from, Observable, of } from 'rxjs';
import { retry, catchError, tap, switchMap, map } from 'rxjs/operators';

export interface ConfigData {
    baseUrl: string;
    msalConfig: Configuration;
}

@Injectable({
    providedIn: 'root',
})
export class ConfigService {
    public data!: Partial<ConfigData>;

    public load(defaults: Partial<ConfigData>): Observable<Partial<ConfigData>> {
        const self = this;

        return from(fetch('assets/configs.json')).pipe(
            switchMap((r, _i) => from(r.json())),
            map((r: { [hostname: string]: ConfigData }) => {
                return r[window.location.hostname];
            }),
            retry(3),
            catchError(() => of(defaults)),
            tap({
                next(response) {
                    self.data = Object.assign(defaults, response);
                },
            })
        );
    }
}
