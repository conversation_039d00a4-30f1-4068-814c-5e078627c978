import {
    AfterContentInit,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    Output,
    ViewChild,
} from '@angular/core';
import { LINE_CHART_OPTIONS } from '@com/const';
import { CellDesignService, CellDesignWithId } from '@com/services/cell-design.service';
import { deepCloneDto } from '@com/utils/object';
import { UIChart } from 'primeng/chart';
import {
    CellOptimization,
    OptimizationAlgorithmEnum,
    OptimizationService,
    OptimizationStatusEnum,
} from '@com/services/optimization.service';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'primeng/api';
import { ToastPositionEnum, ToastSeverityEnum } from '@com/app/prime-ng';
import { Subject, switchMap, takeUntil, timer } from 'rxjs';

export enum ChartTypeEnum {
    line = 'line',
    scatter = 'scatter',
}

@Component({
    selector: 'com-parameter-optimization',
    templateUrl: './parameter-optimization.component.html',
})
export class ParameterOptimizationComponent implements AfterContentInit, OnD<PERSON>roy {
    @ViewChild(UIChart)
    public uiChart: UIChart;

    @Output()
    public designChange = new EventEmitter<CellDesignWithId>();

    @Output()
    public optimizationPendingStatusChange = new EventEmitter<boolean>();

    @Input()
    public set design(value: CellDesignWithId | undefined) {
        if (value) {
            this._design = value;
        }
    }

    public cellOptimization: CellOptimization;

    public chartTypeOptions = [
        { name: 'Line', id: ChartTypeEnum.line },
        { name: 'Scatter', id: ChartTypeEnum.scatter },
    ];

    public chartType: ChartTypeEnum = ChartTypeEnum.line;

    public chartDatasetOptions: {
        name: string;
        id: number;
    }[];

    public get design(): CellDesignWithId | undefined {
        return this._design;
    }

    public OptimizationStatusEnum = OptimizationStatusEnum;

    public OptimizationAlgorithmEnum = OptimizationAlgorithmEnum;

    // chart.js have the chart options defined as any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public options: any = {
        ...deepCloneDto(LINE_CHART_OPTIONS.rootOptions),
        plugins: deepCloneDto(LINE_CHART_OPTIONS.plugins),
        scales: deepCloneDto(LINE_CHART_OPTIONS.scales),
    };

    public selectedChartDataset: number;

    public timeRemaining: string | null = null;

    public loadingDots: string = '';

    private _loadingDotsInterval: ReturnType<typeof setInterval>;

    private _design: CellDesignWithId | undefined;
    private readonly _ngUnsubscribe = new Subject<void>();
    private readonly _ngPollUnsubscribe = new Subject<void>();

    public constructor(
        private _cdr: ChangeDetectorRef,
        private _optimizationService: OptimizationService,
        private readonly _cellDesignService: CellDesignService,
        private _route: ActivatedRoute,
        private _messageService: MessageService
    ) {}

    public ngAfterContentInit(): void {
        if (this.design && this.design.optimizationId) {
            this.startLoadingDotsAnimation();
            this.fetchOptimizationData();
        }
    }

    public ngOnDestroy(): void {
        this.stopLoadingDotsAnimation();
        this._ngUnsubscribe.next();
        this._ngUnsubscribe.complete();
        // Clear interval on component destruction to avoid memory leaks
        this.stopPolling();
    }

    public onChartDatasetChange(selectedIndex: number): void {
        this.options.scales.x.title.text = this.cellOptimization.chart.datasets[selectedIndex].description;
        this.chartDatasetOptions.forEach((option, index) => {
            const isVisible = index === selectedIndex;
            this.uiChart._data.datasets[index].hidden = !isVisible;
            this.uiChart?.chart.setDatasetVisibility(index, isVisible);
        });

        this.uiChart?.chart.update();
    }

    public onChartTypeChange(type: ChartTypeEnum): void {
        this.chartType = type;
        this.options = { ...this.options, pointStyle: type === ChartTypeEnum.scatter };
        this.onChartDatasetChange(this.selectedChartDataset);
    }

    private fetchOptimizationData(): void {
        if (this.design && this.design.optimizationId) {
            this._optimizationService
                .getOptimizationData(this.design?.optimizationId)
                .pipe(takeUntil(this._ngUnsubscribe))
                .subscribe((response) => {
                    this.cellOptimization = response;
                    this.setChartAxisTitles(response);
                    this.setInitialDatasetVisibility();
                    if (response.status === OptimizationStatusEnum.pending && response.progress > 0) {
                        this.calculateTimeRemaining(response);
                    }

                    // If status is pending, start polling every 5 seconds
                    if (response.status === OptimizationStatusEnum.pending) {
                        this._messageService.add({
                            key: ToastPositionEnum.topCenter,
                            severity: ToastSeverityEnum.warning,
                            summary: $localize`:@@toast.optimizationRunningTitle:Optimierungsprozess läuft noch`,
                            detail: $localize`:@@toast.optimizationRunningDesc:Optimierung noch nicht abgeschlossen. Bitte warte bis der Optimierungsprozess abgeschlossen ist.`,
                        });
                        this.optimizationPendingStatusChange.emit(true);
                        this.startPolling();
                    }
                });
        }
    }

    private calculateTimeRemaining(cellOptimization: CellOptimization): void {
        const createdAt = new Date(cellOptimization.createdAt!).getTime();
        const now = Date.now();
        const elapsedTime = now - createdAt; // Time in milliseconds
        const progress = cellOptimization.progress / 100; // Convert percentage to decimal

        if (progress > 0) {
            const estimatedTotalTime = elapsedTime / progress; // Total estimated time
            const remainingTime = estimatedTotalTime - elapsedTime; // Remaining time in milliseconds

            // Convert remaining time to a human-readable format (e.g., "1d 3h 2m 30s")
            const days = Math.floor(remainingTime / (24 * 60 * 60 * 1000));
            const hours = Math.floor((remainingTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
            const minutes = Math.floor((remainingTime % (60 * 60 * 1000)) / 60000);
            const seconds = Math.floor((remainingTime % 60000) / 1000);

            this.timeRemaining = `${days > 0 ? days + 'd ' : ''}${
                hours > 0 ? hours + 'h ' : ''
            }${minutes}m ${seconds}s`;
        } else {
            this.timeRemaining = null;
        }
    }

    private startPolling(): void {
        this._ngPollUnsubscribe.next();

        if (this.design && this.design.optimizationId) {
            // Poll every 5 seconds
            timer(0, 5000)
                .pipe(
                    switchMap(() => {
                        return this._optimizationService.getOptimizationData(this.design!.optimizationId!);
                    }),
                    takeUntil(this._ngPollUnsubscribe),
                    takeUntil(this._ngUnsubscribe)
                )
                .subscribe((response) => {
                    if (response.status === OptimizationStatusEnum.pending && response.progress > 0) {
                        this.calculateTimeRemaining(response);
                    }

                    this.cellOptimization = response;
                    this.setChartAxisTitles(response);

                    if (response.status !== OptimizationStatusEnum.pending) {
                        const id = this._route.snapshot.params['id'];
                        this._cellDesignService.getSingleCellDesign(id).subscribe((design) => {
                            this.designChange.emit(design);
                        });
                        this._messageService.add({
                            key: ToastPositionEnum.topCenter,
                            severity: ToastSeverityEnum.success,
                            summary: $localize`:@@toast.optimizationCompletedTitle:Optimierungsprozess abgeschlossen`,
                            detail: $localize`:@@toast.optimizationCompletedDesc:Zelldesign wurde aktualisiert.`,
                        });
                        this.optimizationPendingStatusChange.emit(false);
                        this.stopPolling(); // Stop polling when status is no longer pending
                    }
                });
        }
    }

    private stopPolling(): void {
        this._ngPollUnsubscribe.next();
    }

    private setChartAxisTitles(cellOptimizationData: CellOptimization): void {
        this.chartDatasetOptions = cellOptimizationData?.chart?.datasets.map(
            (dataset: { description: string }, index: number) => {
                return { id: index, name: dataset.description };
            }
        );
        this.options.scales.x.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);
        this.options.scales.x.title.text = cellOptimizationData.variables[0].name;

        this.options.scales.y.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);
        this.options.scales.y.title.text = cellOptimizationData.objective.name;
        this._cdr.detectChanges();
    }

    private setInitialDatasetVisibility(): void {
        if (this.uiChart) {
            this.selectedChartDataset = this.chartDatasetOptions[0].id;
            this.uiChart.chart.setDatasetVisibility(0, true);
            for (let i = 1; i < this.chartDatasetOptions.length; i++) {
                this.uiChart.chart.setDatasetVisibility(i, false);
            }

            this.uiChart.chart.update();
        }
    }

    private startLoadingDotsAnimation(): void {
        let dotCount = 1;
        this._loadingDotsInterval = setInterval(() => {
            this.loadingDots = '.'.repeat(dotCount);
            dotCount = dotCount === 3 ? 0 : dotCount + 1;
        }, 500);
    }

    private stopLoadingDotsAnimation(): void {
        if (this._loadingDotsInterval) {
            clearInterval(this._loadingDotsInterval);
        }
    }
}
