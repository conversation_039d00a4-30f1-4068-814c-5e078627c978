import { MonoTypeOperatorFunction, Observable } from 'rxjs';

export function delayTick<T>(): MonoTypeOperatorFunction<T> {
    return (source) => {
        let timeout: ReturnType<typeof setTimeout> | null = null;
        const queue: (() => void)[] = [];

        const schedule = (callback: () => void): void => {
            timeout = setTimeout(() => {
                callback();
                timeout = null;

                const next = queue.shift();
                if (next) {
                    schedule(next);
                }
            }, 0);
        };

        const enqueue = (callback: () => void): void => {
            if (timeout == null) {
                schedule(callback);
            } else {
                queue.push(callback);
            }
        };

        return new Observable<T>((subscriber) => {
            let closed = false;

            const subscription = source.subscribe({
                next: (value) => {
                    if (!closed) {
                        enqueue(() => subscriber.next(value));
                    }
                },
                complete: () => {
                    if (!closed) {
                        closed = true;
                        enqueue(() => subscriber.complete());
                    }
                },
                error: (err) => {
                    if (!closed) {
                        closed = true;
                        enqueue(() => subscriber.error(err));
                    }
                },
            });

            return () => {
                closed = true;

                // stop pending timeouts
                if (timeout) {
                    clearTimeout(timeout);
                    timeout = null;
                }

                // unsubscribe from source
                subscription.unsubscribe();
            };
        });
    };
}
