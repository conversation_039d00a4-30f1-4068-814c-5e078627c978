<div *ngIf="!socToolData" class="flex justify-content-center">
    <p-progressSpinner />
</div>

<div *ngIf="socToolData" class="grid align-items-stretch p-3">
    <div class="col-12 lg:col-6">
        <div class="field grid">
            <label [for]="controlNames.socInput" class="break-word col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.soc"
                >SoC</label
            >
            <div class="col-12 xl:col-9 p-fluid">
                <p-inputNumber
                    comCustomizeInput
                    [inputId]="controlNames.socInput"
                    [(ngModel)]="soc"
                    (ngModelChange)="onSocValueChange()"
                    suffix=" %"
                    [min]="0"
                    [max]="100"
                    [maxFractionDigits]="2"
                    mode="decimal"
                ></p-inputNumber>
            </div>
        </div>
        <div class="field grid">
            <label
                [for]="controlNames.voltageInput"
                class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                i18n="@@common.voltageLithium"
                >Spannung vs. Li</label
            >
            <div class="col-12 xl:col-9 p-fluid">
                <p-inputNumber
                    comCustomizeInput
                    [inputId]="controlNames.voltageInput"
                    [(ngModel)]="voltage"
                    [maxFractionDigits]="2"
                    (ngModelChange)="onVoltageChange()"
                    [min]="socToolData.minVoltage"
                    [max]="socToolData.maxVoltage"
                    mode="decimal"
                ></p-inputNumber>
            </div>
        </div>

        <com-summary-table
            *ngIf="tableCells.length > 0"
            class="block mt-4"
            [headers]="tableHeaders"
            [rows]="tableCells"
        ></com-summary-table>
    </div>

    <div class="col-12 lg:col-6">
        <div class="ml-4 flex align-items-center">
            <span i18n="@@common.charge">Laden</span>
            <p-inputSwitch
                class="mx-3"
                [(ngModel)]="isDischargeMode"
                (ngModelChange)="onSocModeChange()"
            ></p-inputSwitch>
            <span i18n="@@common.discharge">Entladen</span>
        </div>
        <com-material-chart [data]="socToolCalculatedData?.chartData" [options]="options" [loading]="!socToolData" />
    </div>
</div>
