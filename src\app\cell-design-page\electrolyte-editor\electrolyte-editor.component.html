<div *ngIf="loading; else electrolyteEditor" class="flex justify-content-center">
    <p-progressSpinner />
</div>

<ng-template #electrolyteEditor>
    <div *ngIf="design" class="p-3">
        <div class="grid align-items-stretch">
            <div class="col-12 lg:col-6">
                <p-panel i18n-header="@@common.amountOfElectrolyte" header="Elektrolytmenge">
                    <div class="css-grid">
                        <span class="break-word" i18n="@@common.poreVolume">Porenvolumen</span>
                        <span i18n="@@common.units.mililitrePerAmperHour"
                            >{{ metrics?.poreVolumeAh | numberTwoFractionDigits }} ml/Ah</span
                        >
                        <span i18n="@@common.units.mililitre"
                            >{{ metrics?.poreVolume | numberTwoFractionDigits }} ml</span
                        >

                        <strong class="break-word" i18n="@@common.amountOfElectrolyte">Elektrolytmenge</strong>
                        <span>
                            <p-inputNumber
                                comCustomizeInput
                                [name]="controlNames.electrolytAmount"
                                [inputId]="controlNames.electrolytAmount"
                                [(ngModel)]="design.electrolytAmount"
                                [maxFractionDigits]="3"
                                mode="decimal"
                                suffix=" ml/Ah"
                                [min]="0"
                                [max]="3"
                                [required]="true"
                            ></p-inputNumber>
                        </span>
                        <strong i18n="@@common.units.mililitre"
                            >{{ metrics?.electrolyteAmount | numberTwoFractionDigits }} ml</strong
                        >

                        <strong class="break-word" i18n="@@electrolyte.suggestedAmountOfElectrolyte">
                            Vorschlag für Elektrolytmenge (mit Puffer für Formierung)
                        </strong>
                        <strong i18n="@@common.units.mililitrePerAmperHour"
                            >{{ metrics?.electrolyteAmountSuggestionAh | numberTwoFractionDigits }} ml/Ah</strong
                        >
                        <strong i18n="@@common.units.mililitre"
                            >{{ metrics?.electrolyteAmountSuggestion | numberTwoFractionDigits }} ml</strong
                        >
                    </div>
                </p-panel>
            </div>
            <div class="col-12 lg:col-6">
                <p-panel i18n-header="@@common.aging" header="Alterung">
                    <div class="css-grid">
                        <span class="break-word" i18n="@@electrolyte.electrolyteConsumptionThroughSeiFormation">
                            Elektrolytverbrauchen durch SEI Bildung
                        </span>
                        <span i18n="@@common.units.mililitrePerAmperHour"
                            >{{ metrics?.electrolyteAmountSei | numberTwoFractionDigits }} ml/Ah</span
                        >
                        <span class="break-word" i18n="@@electrolyte.estimatedFromSeiReaction" class="com-text-warning"
                            >Abgeschätzt aus SEI-Reaktion</span
                        >

                        <span class="break-word" i18n="@@electrolyte.firstCycleEfficiency">First Cycle Efficiency</span>
                        <span i18n="@@common.percentageNumber">
                            {{ metrics?.firstCycleEfficiency | numberTwoFractionDigits }}&nbsp;%
                        </span>
                        <span class="break-word" i18n="@@common.outOfBalancing">aus Balancing</span>

                        <span class="break-word" i18n="@@electrolyte.seiGrowthPerCharge">SEI Wachstum pro Ladung</span>
                        <span i18n="@@common.units.mililitrePerAmperHour"
                            >{{ metrics?.seiGrowthMlAh | numberTwoFractionDigits }} ml/Ah</span
                        >
                        <span i18n="@@common.units.nanometersPerAmperhour"
                            >{{ metrics?.seiGrowthNmAh | numberTwoFractionDigits }} nm/Ah</span
                        >

                        <span class="break-word" i18n="@@electrolyte.activeSurfaceAnode">Aktive Oberfläche Anode</span>
                        <span i18n="@@common.units.squareMeter"
                            >{{ metrics?.anodeActiveSurface | numberTwoFractionDigits }} m²</span
                        >
                        <span class="break-word" i18n="@@common.informationFromManufacturer"
                            >aus Herstellerangaben</span
                        >
                    </div>
                </p-panel>
            </div>
        </div>

        <com-summary-table
            *ngIf="metrics"
            [headers]="metrics.agingTable.headers"
            [hasCellBeenEdited]="hasCellBeenEdited"
            [rows]="metrics.agingTable.rows"
            (cellValueChange)="onCellValueChange($event)"
            (cellValueReset)="onCellValueReset($event)"
            class="mt-5 block"
        ></com-summary-table>

        <div class="mt-5 com-text-warning text-xl break-word">
            <div class="text-center" i18n="@@electrolyte.warning.message">
                Erste Abschätzung Electrolytverbrauch durch SEI Reaktion
            </div>
            <div class="text-center">FEC + 4Li^+ + 4e^- → Lif + 0.5 Li2O + 0.5 Polymer + CO2 + 0.5 H2</div>
            <div class="text-center" i18n="@@electrolyte.warning.source">
                (Quelle: {{ "https://iopscience.iop.org/article/10.1149/2.0951608jes/meta" }})
            </div>
            <div class="text-center" i18n="@@electrolyte.warning.request">
                Hier brauchen wir noch zusätzlichen Input !
            </div>
        </div>
    </div>
</ng-template>
