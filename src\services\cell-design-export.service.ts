import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, tap } from 'rxjs';
import { blobToDataUrl } from '@com/utils/blob-to-data-url';
import { CellDesign } from './cell-design.service';
import { ConfigService } from './config.service';

@Injectable({
    providedIn: 'root',
})
export class CellDesignExportService {
    public constructor(private _config: ConfigService, private _http: HttpClient) {}

    public exportFile(cellDesign: CellDesign, exportType: string, fileExtension: string): Observable<string> {
        const options: {} = Object.assign({}, { responseType: 'blob' });

        return this._http
            .post<Blob>(`${this._config.data.baseUrl}/api/cell-design-export/${exportType}/`, cellDesign, options)
            .pipe(
                blobToDataUrl(),
                tap((value) => {
                    const link = document.createElement('a');
                    link.href = value;
                    link.download = `Export.${fileExtension}`;
                    link.click();
                })
            );
    }

    public downloadExcelExportMultipleDesigns(cellDesignIds: string[], filename: string): Observable<string> {
        const options: {} = Object.assign({}, { responseType: 'blob' });

        return this._http
            .post<Blob>(`${this._config.data.baseUrl}/api/cell-design-export/excel/multiple`, cellDesignIds, options)
            .pipe(
                blobToDataUrl(),
                tap((value) => {
                    const link = document.createElement('a');
                    link.href = value;
                    link.download = filename;
                    link.click();
                })
            );
    }
}
