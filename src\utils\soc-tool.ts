import { MaterialLineChartData, MaterialLineChartDataset } from '@com/services/cell-design-metrics.service';
import { ExtrapolateModeEnum, interpolate } from './interpolate';

export enum SoCModeEnum {
    charging,
    discharging,
}

export interface SoCData {
    soc: number;
    capacity: number;
    voltage: number;
    concentration: number | null;
}

export interface SoCToolCalculatedData {
    chartData: MaterialLineChartData;
    chartAnnotations: {
        [key: string]: { type: 'point'; xValue: number; yValue: number; backgroundColor: string; radius: number };
    };
    cell: SoCData;
    cathode: {
        blend: SoCData;
        material1: SoCData | null;
        material2: SoCData | null;
    };
    anode: {
        blend: SoCData;
        material1: SoCData | null;
        material2: SoCData | null;
    };
}

interface DataPoint {
    x: number;
    y: number;
}

function interpolateForY(
    data: DataPoint[],
    x: number,
    extrapolate: ExtrapolateModeEnum = ExtrapolateModeEnum.clamp
): number | null {
    return interpolate(data, x, (item) => ({ x: item.x, y: item.y }), extrapolate);
}

function interpolateForX(
    data: DataPoint[],
    y: number,
    extrapolate: ExtrapolateModeEnum = ExtrapolateModeEnum.clamp
): number | null {
    return interpolate(data, y, (item) => ({ x: item.y, y: item.x }), extrapolate);
}

export class SoCToolData {
    public set mode(mode: SoCModeEnum) {
        switch (mode) {
            case SoCModeEnum.charging:
                this._datasetIds = [
                    'cell_formation_first_charge',
                    'cell_formation_first_discharge',
                    'cell_formation_second_charge',
                    'cell_c10_charge',
                    'cathode_c10_charge',
                    'anode_c10_charge',
                ];
                this._cellDataset = this.getDataset('cell_c10_charge')?.data ?? null;
                this._cathodeDataset = this.getDataset('cathode_c10_charge')?.data ?? null;
                this._anodeDataset = this.getDataset('anode_c10_charge')?.data ?? null;
                break;

            case SoCModeEnum.discharging:
                this._datasetIds = [
                    'cell_formation_first_charge',
                    'cell_formation_first_discharge',
                    'cell_formation_second_charge',
                    'cell_c10_discharge',
                    'cathode_c10_discharge',
                    'anode_c10_discharge',
                ];
                this._cellDataset = this.getDataset('cell_c10_discharge').data ?? null;
                this._cathodeDataset = this.getDataset('cathode_c10_discharge').data ?? null;
                this._anodeDataset = this.getDataset('anode_c10_discharge').data ?? null;
                break;
        }

        this.minVoltage = this.calculateSoCToolData(0)!.cell.voltage;
        this.maxVoltage = this.calculateSoCToolData(100)!.cell.voltage;
    }

    public minVoltage: number;

    public maxVoltage: number;

    private _datasetIds: string[];

    private _cellDataset: DataPoint[];
    private _cathodeDataset: DataPoint[];
    private _anodeDataset: DataPoint[];

    private _cellDensity: number | null;
    private _cathodeDensity: number | null;
    private _anodeDensity: number | null;

    private _chart: MaterialLineChartData;

    public constructor(chart: MaterialLineChartData, cathodeDensity: number, anodeDensity: number, mode?: SoCModeEnum) {
        this._chart = chart;

        this.mode = mode ? mode : SoCModeEnum.charging;

        this._cellDensity = null;
        this._cathodeDensity = cathodeDensity;
        this._anodeDensity = anodeDensity;
    }

    public getSoCValue(cellVoltage: number): number {
        const capacity = interpolateForX(this._cellDataset, cellVoltage, ExtrapolateModeEnum.clamp) ?? 0;
        const soc = this.getFractionForValue(this._cellDataset, capacity) * 100;

        return soc;
    }

    public calculateSoCToolData(soc: number): SoCToolCalculatedData | null {
        if (!this._cellDataset || !this._cathodeDataset || !this._anodeDataset) {
            return null;
        }

        const capacity = this.getValueAtFraction(this._cellDataset, soc / 100);
        const cell = this.getSoCData(this._cellDataset, capacity, this._cellDensity);
        const cathode = this.getSoCData(this._cathodeDataset, capacity, this._cathodeDensity);
        const anode = this.getSoCData(this._anodeDataset, capacity, this._anodeDensity);

        return {
            chartData: {
                headers: this._chart.headers.slice(),
                datasets: this._chart.datasets.map((ds) => ({
                    ...ds,
                    hidden: !(ds.id && this._datasetIds.includes(ds.id)),
                })),
            },
            chartAnnotations: {
                cell: {
                    type: 'point',
                    xValue: capacity,
                    yValue: cell.voltage,
                    backgroundColor: '#D95319',
                    radius: 5,
                },
                cathode: {
                    type: 'point',
                    xValue: capacity,
                    yValue: cathode.voltage,
                    backgroundColor: '#D95319',
                    radius: 5,
                },
                anode: {
                    type: 'point',
                    xValue: capacity,
                    yValue: anode.voltage,
                    backgroundColor: '#D95319',
                    radius: 5,
                },
            },
            cell: cell,
            cathode: {
                blend: cathode,
                material1: null,
                material2: null,
            },
            anode: {
                blend: anode,
                material1: null,
                material2: null,
            },
        };
    }

    private getSoCData(data: DataPoint[], capacity: number, density: number | null): SoCData {
        const soc = this.getFractionForValue(data, capacity) * 100;
        const voltage = interpolateForY(data, capacity) ?? 0;

        return {
            capacity: capacity,
            soc: soc,
            voltage: voltage,
            concentration: density !== null ? (capacity * density * 1e6) / (96485 * 3600) : null,
        };
    }

    private getValueAtFraction(data: DataPoint[], fraction: number): number {
        const minX = data[0].x;
        const maxX = data[data.length - 1].x;

        return minX + (maxX - minX) * fraction;
    }

    private getFractionForValue(data: DataPoint[], x: number): number {
        const minX = data[0].x;
        const maxX = data[data.length - 1].x;

        return (x - minX) / (maxX - minX);
    }

    private getDataset(id: string): MaterialLineChartDataset {
        const datasetId = this._chart.datasets.findIndex((dataset) => dataset.id === id);
        if (datasetId >= 0) {
            return this._chart.datasets[datasetId];
        } else {
            throw new Error(`Dataset with id ${id} not present in datasets`);
        }
    }
}
