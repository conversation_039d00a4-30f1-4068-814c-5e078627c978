{"name": "cell-o-mat", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint --max-warnings 0", "i18n": "npm run ng extract-i18n"}, "private": true, "dependencies": {"@angular/animations": "^15.1.0", "@angular/common": "^15.1.0", "@angular/compiler": "^15.1.0", "@angular/core": "^15.1.0", "@angular/forms": "^15.1.0", "@angular/localize": "^15.1.0", "@angular/platform-browser": "^15.1.0", "@angular/platform-browser-dynamic": "^15.1.0", "@angular/router": "^15.1.0", "@azure/msal-angular": "^2.5.4", "@azure/msal-browser": "^2.33.0", "chart.js": "^4.2.1", "chartjs-plugin-annotation": "^2.1.2", "primeflex": "^3.3.0", "primeicons": "^6.0.1", "primeng": "^15.2.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.1.3", "@angular-eslint/builder": "15.2.1", "@angular-eslint/eslint-plugin": "15.2.1", "@angular-eslint/eslint-plugin-template": "15.2.1", "@angular-eslint/schematics": "15.2.1", "@angular-eslint/template-parser": "15.2.1", "@angular/cli": "^15.2.10", "@angular/compiler-cli": "^15.1.0", "@types/jasmine": "~4.3.0", "@typescript-eslint/eslint-plugin": "5.48.2", "@typescript-eslint/parser": "5.48.2", "angular-eslint": "^0.0.1-alpha.0", "eslint": "^8.33.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "prettier": "^2.8.4", "prettier-eslint": "^15.0.1", "typescript": "~4.9.4"}}