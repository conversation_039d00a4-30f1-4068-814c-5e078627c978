import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';

export type AutocompleteEndpoint = 'project' | 'project-state';

@Injectable({ providedIn: 'root' })
export class AutocompleteService {
    public constructor(private readonly _http: HttpClient, private readonly _config: ConfigService) {}

    public get(endpoint: AutocompleteEndpoint, value: string | null, limit?: number): Observable<string[]> {
        const params = new HttpParams().set('search', value ?? '').set('limit', limit ?? 100);

        return this._http.get<string[]>(`${this._config.data.baseUrl}/api/autocomplete/${endpoint}`, { params });
    }
}
